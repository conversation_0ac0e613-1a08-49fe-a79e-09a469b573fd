import React, { useEffect, useCallback, useRef } from 'react';
import { Button, message, Space, Typography } from 'antd';
import { SaveOutlined, PlayCircleOutlined, FullscreenOutlined, MenuUnfoldOutlined, MenuFoldOutlined } from '@ant-design/icons';
import {
  DesignerProvider,
  Canvas,
  PropertyDrawer,
  useDesigner,
  DesignComponentSidebar
} from '@lowcode/designer';
import {
  SchemaLoader
} from '@lowcode/shared';

const { Title } = Typography;


// 设计器主界面组件
const DesignerApp: React.FC = () => {
  const { schema, updateSchema, canvasState, toggleComponentPanel } = useDesigner();
  const hasLoadedRef = useRef(false);

  // 保存Schema到服务器（固定保存为 api-management.json）
  const handleSaveSchema = useCallback(async () => {
    try {
      // 确保 schema 的 id 是 api_management
      const schemaToSave = {
        ...schema,
        id: 'api_management'
      };

      await SchemaLoader.updateOnServer('api-management.json', schemaToSave);

      message.success('Schema已保存到服务器');
    } catch (error) {
      console.error('Failed to save schema:', error);
      message.error('保存失败');
    }
  }, [schema]);



  // 预览页面
  const handlePreview = useCallback(() => {
    // 保存当前Schema到localStorage供运行时应用使用
    localStorage.setItem('lowcode-runtime-schema', JSON.stringify(schema));

    // 打开运行时应用
    window.open('http://localhost:3003', '_blank');
    message.info('已在新窗口打开预览，请确保运行时应用已启动');
  }, [schema]);

  // 全屏预览功能（仿照simple-app的预览逻辑）
  const handleFullscreenPreview = useCallback(() => {
    // 将schema数据存储到sessionStorage
    sessionStorage.setItem('lowcode-preview-schema', JSON.stringify(schema));

    // 打开新窗口到预览页面
    const previewUrl = `${window.location.origin}${window.location.pathname}?mode=preview`;
    window.open(previewUrl, '_blank', 'width=1200,height=800');
    message.info('已在新窗口打开全屏预览');
  }, [schema]);

  // 初始化时直接从服务器加载 api-management.json
  useEffect(() => {
    // 防止 React.StrictMode 导致的重复执行
    if (hasLoadedRef.current) return;
    hasLoadedRef.current = true;

    const loadInitialSchema = async () => {
      try {
        // 直接从服务器加载 api-management.json
        const serverSchema = await SchemaLoader.loadFromServer('api-management.json');
        updateSchema(serverSchema);
        message.success('已从服务器加载Schema');
      } catch (error) {
        console.error('Failed to load schema from server:', error);
        message.error('从服务器加载Schema失败，请检查服务器连接');

        // 如果服务器加载失败，创建一个基本的空Schema
        const fallbackSchema = {
          id: 'api_management',
          title: '新建页面',
          components: []
        };
        updateSchema(fallbackSchema);
      }
    };

    loadInitialSchema();
  }, []); // 移除 updateSchema 依赖，只在组件挂载时执行一次

  return (
    <div style={{ height: '100vh', display: 'flex', flexDirection: 'column' }}>
      {/* 顶部工具栏 */}
      <div style={{
        padding: '12px',
        borderBottom: '1px solid #f0f0f0',
        background: '#fff',
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center'
      }}>
        {/* 左侧：标题 */}
        <div style={{ display: 'flex', alignItems: 'center' }}>
          <Title level={3} style={{ margin: 0 }}>
            🎨 低代码开发平台
          </Title>
        </div>

        {/* 右侧：操作按钮 */}
        <Space>
          <Button
            type="default"
            icon={canvasState.componentPanelCollapsed ? <MenuUnfoldOutlined /> : <MenuFoldOutlined />}
            onClick={toggleComponentPanel}
          >
            {canvasState.componentPanelCollapsed ? '展开' : '收缩'}
          </Button>
          <Button
            type="primary"
            icon={<SaveOutlined />}
            onClick={handleSaveSchema}
          >
            保存
          </Button>
          <Button
            type="default"
            icon={<PlayCircleOutlined />}
            onClick={handlePreview}
          >
            预览
          </Button>
          <Button
            type="default"
            icon={<FullscreenOutlined />}
            onClick={handleFullscreenPreview}
          >
            全屏
          </Button>
        </Space>
      </div>

      {/* 主要设计区域 */}
      <div style={{ flex: 1, display: 'flex', overflow: 'hidden' }}>
        {/* 左侧组件面板 */}
        <DesignComponentSidebar />

        {/* 中间画布区域 */}
        <div style={{ flex: 1, background: '#f5f5f5', position: 'relative', minWidth: 0 }}>
          <Canvas />
        </div>

        {/* 右侧属性面板 */}
        <PropertyDrawer />
      </div>
    </div>
  );
};

// 创建一个基本的初始Schema，实际数据会在DesignerApp中从服务器加载
const initialSchema = {
  id: 'api_management',
  title: '加载中...',
  components: []
};

// 主应用组件
const App: React.FC = () => {
  return (
    <DesignerProvider initialSchema={initialSchema}>
      <DesignerApp />
    </DesignerProvider>
  );
};

export default App;
