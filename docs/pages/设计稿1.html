<!DOCTYPE html>
<html lang="zh"><head>
<meta charset="utf-8"/>
<meta content="width=device-width, initial-scale=1.0" name="viewport"/>
<title>低代码平台示例</title>
<script src="https://cdn.tailwindcss.com?plugins=forms,container-queries"></script>
<link href="https://fonts.googleapis.com/icon?family=Material+Icons|Material+Icons+Outlined" rel="stylesheet"/>
<link href="https://fonts.googleapis.com" rel="preconnect"/>
<link crossorigin="" href="https://fonts.gstatic.com" rel="preconnect"/>
<link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&amp;display=swap" rel="stylesheet"/>
<script defer="" src="https://cdn.jsdelivr.net/gh/alpinejs/alpine@v3.x.x/dist/cdn.min.js"></script>
<style type="text/tailwindcss">
    body {
      font-family: 'Inter', sans-serif;
    }
    [x-cloak] {
      display: none !important;
    }
    .tab-active {
      @apply bg-blue-50 text-blue-600 border-blue-500;
    }
    .tab-inactive {
      @apply text-gray-500 hover:text-gray-700 hover:bg-gray-100 border-transparent;
    }
    .component-outline {
        @apply ring-2 ring-blue-500 ring-offset-2;
    }
    .component-hover:hover {
        @apply ring-2 ring-blue-500 ring-offset-2;
    }
  </style>
</head>
<body class="bg-gray-100">
<div class="flex h-screen flex-col" x-data="{ showPropertyPanel: false, componentsPanel: 'basics', isComponentsCollapsed: false, selectedComponent: null }">
<header class="bg-white border-b border-gray-200 px-6 py-3 flex justify-between items-center z-20 flex-shrink-0">
<h1 class="text-xl font-bold text-gray-800">低代码平台</h1>
<div class="flex items-center space-x-4">
<button class="bg-blue-600 text-white font-semibold py-2 px-4 rounded-md hover:bg-blue-700 text-sm">预览
      </button>
</div>
</header>
<div class="flex flex-1 overflow-hidden">
<aside :class="{'w-0': isComponentsCollapsed, 'w-64': !isComponentsCollapsed}" class="bg-white border-r border-gray-200 flex flex-col flex-shrink-0 transition-all duration-300 relative">
<button @click="isComponentsCollapsed = !isComponentsCollapsed" class="absolute -right-3 top-1/2 -translate-y-1/2 z-10 bg-white border border-gray-300 rounded-full p-0.5 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
<span class="material-icons-outlined text-gray-600 transition-transform duration-300" x-show="!isComponentsCollapsed">chevron_left</span>
<span class="material-icons-outlined text-gray-600 transition-transform duration-300" x-cloak="" x-show="isComponentsCollapsed">chevron_right</span>
</button>
<div :class="{'opacity-0': isComponentsCollapsed}" class="flex flex-1 overflow-hidden">
<div class="w-20 border-r border-gray-200 flex-shrink-0">
<nav class="flex flex-col space-y-1 p-2">
<button :class="{ 'tab-active': componentsPanel === 'layout', 'tab-inactive': componentsPanel !== 'layout' }" @click="componentsPanel = 'layout'" class="flex flex-col items-center p-2 rounded-md border-l-4 transition-colors duration-200">
<span class="material-icons text-xl">view_quilt</span>
<span class="text-xs font-medium mt-1">布局</span>
</button>
<button :class="{ 'tab-active': componentsPanel === 'basics', 'tab-inactive': componentsPanel !== 'basics' }" @click="componentsPanel = 'basics'" class="flex flex-col items-center p-2 rounded-md border-l-4 transition-colors duration-200">
<span class="material-icons text-xl">widgets</span>
<span class="text-xs font-medium mt-1">基础</span>
</button>
<button :class="{ 'tab-active': componentsPanel === 'business', 'tab-inactive': componentsPanel !== 'business' }" @click="componentsPanel = 'business'" class="flex flex-col items-center p-2 rounded-md border-l-4 transition-colors duration-200">
<span class="material-icons text-xl">business_center</span>
<span class="text-xs font-medium mt-1">业务</span>
</button>
</nav>
</div>
<div class="flex-1 overflow-y-auto p-3 transition-opacity duration-300">
<div x-show="componentsPanel === 'layout'">
<div class="grid grid-cols-1 gap-2">
<div class="p-2 border border-gray-200 rounded-lg text-center cursor-move hover:bg-gray-50 hover:border-blue-500 transition-all">
<span class="material-icons text-gray-500 text-2xl">view_quilt</span>
<p class="text-xs text-gray-800 mt-1 font-medium">容器</p>
</div>
</div>
</div>
<div x-show="componentsPanel === 'basics'">
<div class="space-y-2">
<div x-data="{ isOpen: true }">
<button @click="isOpen = !isOpen" class="w-full flex justify-between items-center py-1 text-sm font-semibold text-gray-700">
<span class="text-xs">基础组件</span>
<span :class="{ 'rotate-180': isOpen }" class="material-icons text-base transition-transform">expand_more</span>
</button>
<div class="mt-2" x-show="isOpen" x-transition:enter="transition ease-out duration-100" x-transition:enter-end="transform opacity-100 scale-100" x-transition:enter-start="transform opacity-0 scale-95" x-transition:leave="transition ease-in duration-75" x-transition:leave-end="transform opacity-0 scale-95" x-transition:leave-start="transform opacity-100 scale-100">
<div class="grid grid-cols-1 gap-2">
<div class="p-2 border border-gray-200 rounded-lg text-center cursor-move hover:bg-gray-50 hover:border-blue-500 transition-all">
<span class="material-icons text-gray-500 text-2xl">text_fields</span>
<p class="text-xs text-gray-800 mt-1 font-medium">文本</p>
</div>
<div class="p-2 border border-gray-200 rounded-lg text-center cursor-move hover:bg-gray-50 hover:border-blue-500 transition-all">
<span class="material-icons text-gray-500 text-2xl">smart_button</span>
<p class="text-xs text-gray-800 mt-1 font-medium">按钮</p>
</div>
<div class="p-2 border border-gray-200 rounded-lg text-center cursor-move hover:bg-gray-50 hover:border-blue-500 transition-all">
<span class="material-icons text-gray-500 text-2xl">input</span>
<p class="text-xs text-gray-800 mt-1 font-medium">输入框</p>
</div>
<div class="p-2 border border-gray-200 rounded-lg text-center cursor-move hover:bg-gray-50 hover:border-blue-500 transition-all">
<span class="material-icons text-gray-500 text-2xl">image</span>
<p class="text-xs text-gray-800 mt-1 font-medium">图片</p>
</div>
</div>
</div>
</div>
</div>
</div>
<div x-show="componentsPanel === 'business'">
<div class="grid grid-cols-1 gap-2">
<div class="p-2 border border-gray-200 rounded-lg text-center cursor-move hover:bg-gray-50 hover:border-blue-500 transition-all">
<span class="material-icons text-gray-500 text-2xl">table_chart</span>
<p class="text-xs text-gray-800 mt-1 font-medium">表格</p>
</div>
</div>
</div>
</div>
</div>
</aside>
<main @click="selectedComponent = null; showPropertyPanel = false" class="flex-1 overflow-y-auto p-6 bg-gray-50 relative">
<div :class="{'component-outline': showPropertyPanel &amp;&amp; selectedComponent === 'pageContainer'}" @click.stop="" class="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
<div :class="{'component-outline': showPropertyPanel &amp;&amp; selectedComponent === 'header'}" @click.stop="selectedComponent = 'header'; showPropertyPanel = true" class="flex justify-between items-center mb-6 pb-4 border-b border-gray-200 component-hover cursor-pointer">
<div class="flex items-center space-x-2">
<button class="px-3 py-1.5 text-sm font-semibold text-white bg-blue-600 rounded-md">全知科技</button>
<button class="px-3 py-1.5 text-sm font-medium text-gray-600 hover:bg-gray-100 rounded-md">概览</button>
<button class="px-3 py-1.5 text-sm font-medium text-gray-600 hover:bg-gray-100 rounded-md">APIs</button>
<button class="px-3 py-1.5 text-sm font-medium text-gray-600 hover:bg-gray-100 rounded-md">数据</button>
<button class="px-3 py-1.5 text-sm font-medium text-gray-600 hover:bg-gray-100 rounded-md">洞察</button>
<button class="px-3 py-1.5 text-sm font-medium text-gray-600 hover:bg-gray-100 rounded-md">风险</button>
<button class="px-3 py-1.5 text-sm font-medium text-gray-600 hover:bg-gray-100 rounded-md">审计</button>
<button class="px-3 py-1.5 text-sm font-medium text-gray-600 hover:bg-gray-100 rounded-md">配置</button>
</div>
</div>
<div :class="{'component-outline': showPropertyPanel &amp;&amp; selectedComponent === 'searchForm'}" @click.stop="selectedComponent = 'searchForm'; showPropertyPanel = true" class="bg-gray-50 p-6 rounded-lg border border-gray-200 component-hover cursor-pointer">
<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
<div>
<label class="text-sm font-medium text-gray-700 mb-1 block">应用</label>
<select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-gray-500 text-sm">
<option>请选择</option>
</select>
</div>
<div>
<label class="text-sm font-medium text-gray-700 mb-1 block">应用名称</label>
<input class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm" placeholder="请输入" type="text"/>
</div>
<div>
<label class="text-sm font-medium text-gray-700 mb-1 block">访问模式</label>
<select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-gray-500 text-sm">
<option>请选择</option>
</select>
</div>
<div>
<label class="text-sm font-medium text-gray-700 mb-1 block">终端类型</label>
<select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-gray-500 text-sm">
<option>请选择</option>
</select>
</div>
<div>
<label class="text-sm font-medium text-gray-700 mb-1 block">请求类型</label>
<select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-gray-500 text-sm">
<option>请选择</option>
</select>
</div>
<div>
<label class="text-sm font-medium text-gray-700 mb-1 block">响应告警</label>
<select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-gray-500 text-sm">
<option>请选择</option>
</select>
</div>
<div>
<label class="text-sm font-medium text-gray-700 mb-1 block">首次发现时间</label>
<input class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm" placeholder="年 / 月 / 日" type="text"/>
</div>
<div>
<label class="text-sm font-medium text-gray-700 mb-1 block">API敏感等级</label>
<select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-gray-500 text-sm">
<option>请选择</option>
</select>
</div>
</div>
<div class="flex justify-start space-x-2">
<button class="bg-blue-600 text-white font-medium py-2 px-4 rounded-md hover:bg-blue-700 text-sm">搜索
            </button>
<button class="bg-white text-gray-700 font-medium py-2 px-4 rounded-md border border-gray-300 hover:bg-gray-100 text-sm">
              重置
            </button>
</div>
</div>
<div :class="{'component-outline': showPropertyPanel &amp;&amp; selectedComponent === 'table'}" @click.stop="selectedComponent = 'table'; showPropertyPanel = true" class="mt-6 component-hover cursor-pointer">
<div class="flex items-center space-x-4 mb-4">
<button class="flex items-center text-sm text-blue-600 font-medium hover:text-blue-800">
<span class="material-icons text-lg mr-1">file_download</span>导出
            </button>
<button class="flex items-center text-sm text-blue-600 font-medium hover:text-blue-800">
<span class="material-icons text-lg mr-1">refresh</span>刷新
            </button>
</div>
<div class="overflow-x-auto border border-gray-200 rounded-lg">
<table class="w-full text-sm text-left text-gray-500">
<thead class="text-xs text-gray-700 uppercase bg-gray-50">
<tr>
<th class="px-6 py-3" scope="col">路径</th>
<th class="px-6 py-3" scope="col">API敏感等级</th>
<th class="px-6 py-3" scope="col">累计访问次数</th>
<th class="px-6 py-3" scope="col">目检覆盖率</th>
<th class="px-6 py-3" scope="col">IP</th>
<th class="px-6 py-3" scope="col">首次发现时间</th>
<th class="px-6 py-3" scope="col">操作</th>
</tr>
</thead>
<tbody>
<tr class="bg-white border-b hover:bg-gray-50">
<td class="px-6 py-4 font-medium text-gray-900 whitespace-nowrap">/k8s-api/${param1}/${param2}</td>
<td class="px-6 py-4">
<span class="bg-red-100 text-red-800 text-xs font-medium mr-2 px-2.5 py-0.5 rounded-full">高敏感</span>
</td>
<td class="px-6 py-4">6.4k</td>
<td class="px-6 py-4">—</td>
<td class="px-6 py-4">*************</td>
<td class="px-6 py-4">2025-08-14 19:44:25</td>
<td class="px-6 py-4">
<button class="text-blue-600 hover:text-blue-800">
<span class="material-icons text-base">more_horiz</span>
</button>
</td>
</tr>
<tr class="bg-white border-b hover:bg-gray-50">
<td class="px-6 py-4 font-medium text-gray-900 whitespace-nowrap">/login</td>
<td class="px-6 py-4">
<span class="bg-red-100 text-red-800 text-xs font-medium mr-2 px-2.5 py-0.5 rounded-full">高敏感</span>
</td>
<td class="px-6 py-4">3.6k</td>
<td class="px-6 py-4">—</td>
<td class="px-6 py-4">192.168.0.1</td>
<td class="px-6 py-4">2025-08-14 19:38:21</td>
<td class="px-6 py-4">
<button class="text-blue-600 hover:text-blue-800">
<span class="material-icons text-base">more_horiz</span>
</button>
</td>
</tr>
<tr class="bg-white border-b hover:bg-gray-50">
<td class="px-6 py-4 font-medium text-gray-900 whitespace-nowrap">/abnormal</td>
<td class="px-6 py-4">
<span class="bg-red-100 text-red-800 text-xs font-medium mr-2 px-2.5 py-0.5 rounded-full">高敏感</span>
</td>
<td class="px-6 py-4">2.5k</td>
<td class="px-6 py-4">—</td>
<td class="px-6 py-4">192.168.0.11</td>
<td class="px-6 py-4">2025-08-14 19:02:19</td>
<td class="px-6 py-4">
<button class="text-blue-600 hover:text-blue-800">
<span class="material-icons text-base">more_horiz</span>
</button>
</td>
</tr>
<tr class="bg-white border-b hover:bg-gray-50">
<td class="px-6 py-4 font-medium text-gray-900 whitespace-nowrap">/login</td>
<td class="px-6 py-4">
<span class="bg-red-100 text-red-800 text-xs font-medium mr-2 px-2.5 py-0.5 rounded-full">高敏感</span>
</td>
<td class="px-6 py-4">502</td>
<td class="px-6 py-4">—</td>
<td class="px-6 py-4">192.168.0.21</td>
<td class="px-6 py-4">2025-08-14 19:00:18</td>
<td class="px-6 py-4">
<button class="text-blue-600 hover:text-blue-800">
<span class="material-icons text-base">more_horiz</span>
</button>
</td>
</tr>
<tr class="bg-white hover:bg-gray-50">
<td class="px-6 py-4 font-medium text-gray-900 whitespace-nowrap">/login</td>
<td class="px-6 py-4">
<span class="bg-red-100 text-red-800 text-xs font-medium mr-2 px-2.5 py-0.5 rounded-full">高敏感</span>
</td>
<td class="px-6 py-4">327</td>
<td class="px-6 py-4">—</td>
<td class="px-6 py-4">192.168.0.15</td>
<td class="px-6 py-4">2025-08-14 19:00:17</td>
<td class="px-6 py-4">
<button class="text-blue-600 hover:text-blue-800">
<span class="material-icons text-base">more_horiz</span>
</button>
</td>
</tr>
</tbody>
</table>
</div>
</div>
</div>
<div @click.away="contextMenu.show = false" class="absolute bottom-4 left-1/2 -translate-x-1/2 bg-white/80 backdrop-blur-sm rounded-lg shadow-lg border border-gray-200 px-3 py-2 text-sm z-10" x-cloak="" x-data="{ contextMenu: { show: false, x: 0, y: 0, item: null } }" x-show="selectedComponent" x-transition:enter="transition ease-out duration-200" x-transition:enter-end="opacity-100 translate-y-0" x-transition:enter-start="opacity-0 translate-y-2" x-transition:leave="transition ease-in duration-150" x-transition:leave-end="opacity-0 translate-y-2" x-transition:leave-start="opacity-100 translate-y-0">
<nav class="flex items-center space-x-2 text-gray-600">
<button :class="{'bg-blue-100 text-blue-700 font-semibold': selectedComponent === 'pageContainer'}" @contextmenu.prevent="contextMenu.show = true; contextMenu.x = $event.clientX; contextMenu.y = $event.clientY; contextMenu.item = '页面'" class="flex items-center hover:bg-gray-100 hover:text-gray-900 px-2 py-1 rounded-md cursor-move group">
<span class="material-icons text-base mr-1 text-gray-400 group-hover:text-gray-600 transition-colors">drag_indicator</span>
            页面
          </button>
<span class="material-icons text-base">chevron_right</span>
<button :class="{'bg-blue-100 text-blue-700 font-semibold': selectedComponent === 'searchForm'}" @contextmenu.prevent="contextMenu.show = true; contextMenu.x = $event.clientX; contextMenu.y = $event.clientY; contextMenu.item = '视图容器'" class="flex items-center hover:bg-gray-100 hover:text-gray-900 px-2 py-1 rounded-md cursor-move group">
<span class="material-icons text-base mr-1 text-gray-400 group-hover:text-gray-600 transition-colors">drag_indicator</span>
            表格搜索视图
          </button>
<template x-if="selectedComponent === 'table'">
<span>
<span class="material-icons text-base">chevron_right</span>
<button @contextmenu.prevent="contextMenu.show = true; contextMenu.x = $event.clientX; contextMenu.y = $event.clientY; contextMenu.item = '表格'" class="flex items-center bg-blue-100 text-blue-700 font-semibold px-2 py-1 rounded-md cursor-move group">
<span class="material-icons text-base mr-1 text-blue-400 group-hover:text-blue-600 transition-colors">drag_indicator</span>
            表格
          </button>
</span>
</template>
</nav>
<div :style="`left: ${contextMenu.x}px; top: ${contextMenu.y}px`" @click.away="contextMenu.show = false" class="absolute bg-white border border-gray-200 rounded-md shadow-lg py-1 w-48 z-50" x-cloak="" x-show="contextMenu.show">
<a class="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100" href="#">
<span class="material-icons text-base mr-2">content_copy</span> 复制组件
          </a>
<a class="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100" href="#">
<span class="material-icons text-base mr-2">content_paste</span> 粘贴到内部
          </a>
<a class="flex items-center px-4 py-2 text-sm text-red-600 hover:bg-red-50" href="#">
<span class="material-icons text-base mr-2">delete</span> 删除组件
          </a>
</div>
</div>
</main>
<div @click="showPropertyPanel = false; selectedComponent = null" class="fixed inset-0 bg-black bg-opacity-30 z-30" x-cloak="" x-show="showPropertyPanel" x-transition:enter="transition ease-out duration-300" x-transition:enter-end="opacity-100" x-transition:enter-start="opacity-0" x-transition:leave="transition ease-in duration-200" x-transition:leave-end="opacity-0" x-transition:leave-start="opacity-100"></div>
<aside :class="{ 'translate-x-0': showPropertyPanel, 'translate-x-full': !showPropertyPanel }" @click.stop="" class="fixed top-0 right-0 h-full bg-white border-l border-gray-200 p-6 flex flex-col w-96 z-40 transform transition-transform duration-300 ease-in-out" x-cloak="">
<div class="flex-shrink-0 flex justify-between items-center mb-6">
<div>
<h2 class="text-lg font-semibold text-gray-800">已选组件</h2>
<p class="text-sm text-gray-500" x-text="selectedComponent === 'searchForm' ? '表格搜索视图' : (selectedComponent === 'table' ? '表格' : (selectedComponent === 'header' ? '导航栏' : '页面容器')) "></p>
</div>
<button @click="showPropertyPanel = false; selectedComponent = null" class="text-gray-500 hover:text-gray-800">
<span class="material-icons">close</span>
</button>
</div>
<div class="flex-1 overflow-y-auto pr-2 space-y-6">
<div x-show="selectedComponent === 'searchForm' || selectedComponent === 'table'">
<label class="text-sm font-medium text-gray-700 flex items-center">
            名称
            <span class="text-red-500 ml-1">*</span>
</label>
<input :value="selectedComponent === 'searchForm' ? 'searchForm' : 'table'" class="mt-1 w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm" type="text"/>
</div>
<div x-show="selectedComponent === 'table'">
<label class="text-sm font-medium text-gray-700 flex items-center">
            列定义 (columns)
            <span class="text-red-500 ml-1">*</span>
</label>
<textarea class="mt-1 w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 font-mono text-xs bg-gray-50" rows="6">[
  {
    "key": "path",
    "title": "路径",
    "dataIndex": "path",
    "..."
  }
]</textarea>
</div>
<div x-show="selectedComponent === 'table'">
<label class="text-sm font-medium text-gray-700 flex items-center">
            数据源 (dataSource)
            <span class="text-red-500 ml-1">*</span>
</label>
<textarea class="mt-1 w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 font-mono text-xs bg-gray-50" rows="8">[
  {
    "id": 1,
    "path": "/k8s-api/${param1}/${param2}",
    "..."
  }
]
</textarea>
</div>
<div x-show="selectedComponent === 'table'">
<label class="text-sm font-medium text-gray-700 mb-1 block">加载状态 (loading)</label>
<div class="flex items-center space-x-4 mt-2">
<label class="flex items-center">
<input class="form-radio h-4 w-4 text-blue-600 border-gray-300 focus:ring-blue-500" name="loading" type="radio"/>
<span class="ml-2 text-sm text-gray-700">是</span>
</label>
<label class="flex items-center">
<input checked="" class="form-radio h-4 w-4 text-blue-600 border-gray-300 focus:ring-blue-500" name="loading" type="radio"/>
<span class="ml-2 text-sm text-gray-700">否</span>
</label>
</div>
</div>
<div x-show="selectedComponent === 'searchForm'">
<label class="text-sm font-medium text-gray-700">搜索字段 (searchFields)</label>
<textarea class="mt-1 w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 font-mono text-xs bg-gray-50" rows="6">[
  {
    "key": "app",
    "..."
  }
]
</textarea>
</div>
<div x-show="selectedComponent === 'header'">
<label class="text-sm font-medium text-gray-700">导航项 (items)</label>
<textarea class="mt-1 w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 font-mono text-xs bg-gray-50" rows="6">[
  { "key": "overview", "label": "概览" },
  { "key": "apis", "label": "APIs" },
  ...
]
</textarea>
</div>
</div>
</aside>
</div>
</div>
</body></html>