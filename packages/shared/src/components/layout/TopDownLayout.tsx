import React from 'react';

export interface TopDownLayoutProps {
  header?: React.ReactNode;
  sidebar?: React.ReactNode;
  footer?: React.ReactNode;
  children?: React.ReactNode;
  sidebarWidth?: number;
  headerHeight?: number;
  footerHeight?: number;
  style?: React.CSSProperties;
  className?: string;
}

export const TopDownLayout: React.FC<TopDownLayoutProps> = ({
  header,
  sidebar,
  footer,
  children,
  sidebarWidth = 240,
  headerHeight = 64,
  footerHeight = 32,
  style,
  className,
}) => {

  // 如果通过 children 数组传递组件，则从中提取对应的组件
  let headerComponent = header;
  let sidebarComponent = sidebar;
  let footerComponent = footer;
  let mainContent = children;

  if (React.Children.count(children) > 1) {
    const childrenArray = React.Children.toArray(children);

    // 根据组件类型或ID来分配到不同的插槽
    childrenArray.forEach((child: any) => {
      if (React.isValidElement(child)) {
        const props = child.props as any;
        const componentType = props?.['data-component-type'];
        const componentId = props?.['data-component-id'];

        if (componentType === 'TopNavigation' || componentId === 'top_nav') {
          headerComponent = child;
        } else if (componentType === 'SidebarTreeView' || componentId === 'sidebar') {
          sidebarComponent = child;
        } else if (componentType === 'StatusBar' || componentId === 'status_bar') {
          footerComponent = child;
        } else {
          // 其他组件作为主内容
          mainContent = child;
        }
      }
    });
  }

  const layoutStyle: React.CSSProperties = {
    display: 'flex',
    flexDirection: 'column',
    height: '100vh',
    overflow: 'hidden',
    ...style,
  };

  const headerStyle: React.CSSProperties = {
    minHeight: `${headerHeight}px`,
    flexShrink: 0,
    zIndex: 1001,
    backgroundColor: '#fff',
    borderBottom: '1px solid #f0f0f0',
  };

  const bodyStyle: React.CSSProperties = {
    display: 'flex',
    flex: 1,
    overflow: 'hidden',
    minHeight: 0,
  };

  const sidebarStyle: React.CSSProperties = {
    width: `${sidebarWidth}px`,
    flexShrink: 0,
    overflow: 'hidden',
    position: 'relative',
    zIndex: 1000,
  };

  const contentStyle: React.CSSProperties = {
    flex: 1,
    display: 'flex',
    flexDirection: 'column',
    overflow: 'hidden',
    minHeight: 0, // 确保flex子元素能正确收缩
  };

  const mainStyle: React.CSSProperties = {
    flex: 1,
    overflow: 'auto',
    padding: '0',
    backgroundColor: '#f5f6f8',
    minHeight: 0, // 确保flex子元素能正确收缩
  };

  const footerStyle: React.CSSProperties = {
    height: `${footerHeight}px`,
    flexShrink: 0,
    zIndex: 999,
    backgroundColor: '#fff',
    borderTop: '1px solid #f0f0f0',
  };



  return (
    <div className={`lowcode-topdown-layout ${className || ''}`} style={layoutStyle}>
      {/* 头部 */}
      {headerComponent && (
        <div style={headerStyle}>
          {headerComponent}
        </div>
      )}

      {/* 主体区域 */}
      <div style={bodyStyle}>
        {/* 侧边栏 */}
        {sidebarComponent && (
          <div style={sidebarStyle}>
            {sidebarComponent}
          </div>
        )}

        {/* 内容区域 */}
        <div style={contentStyle}>
          <main style={mainStyle}>
            {mainContent}
          </main>
        </div>
      </div>

      {/* 底部 */}
      {footerComponent && (
        <div style={footerStyle}>
          {footerComponent}
        </div>
      )}
    </div>
  );
};
