import React, { useEffect, useState, useCallback } from 'react';
import type { PageSchema } from '@lowcode/renderer';
import { DesignerProv<PERSON>, Can<PERSON>, PropertyDrawer, Toolbar, useDesigner, DesignComponentSidebar } from '@lowcode/designer';

// API 管理页面 Schema（接近截图效果）
const apiPageSchema: PageSchema = {
  id: 'api_management',
  title: '全部API',
  components: [
    { id: 'top_nav', type: 'TopNavigation', props: {
        logo: { 
          src:"data:image/png;base64,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",
          href: '/' },
        menu: [
          { key: 'overview', label: '概览', href: '/overview' },
          { key: 'apis', label: 'APIs', href: '/apis' },
          { key: 'data', label: '数据', href: '/data' },
          { key: 'insight', label: '洞察', href: '/insight' },
          { key: 'risk', label: '风险', href: '/risk' },
          { key: 'audit', label: '审计', href: '/audit' },
          { key: 'config', label: '配置', href: '/config' }
        ],
        user: { name: 'book', menu: [{ key: 'logout', label: '退出' }] }
    }},
    { id: 'main', type: 'Container', props: { style: { display: 'flex', minHeight: 'calc(100vh - 96px)' } },
      children: [
        { id: 'left_sidebar', type: 'Container', props: { style: { width: '240px' } },
          children: [
            { id: 'sidebar', type: 'SidebarTreeView', props: {
                width: 240, height: '100%', searchable: true, searchPlaceholder: '搜索视图/分类',
                data: [
                  { key: 'all', title: '全部API', count: 212, href: '/apis/all' },
                  {
                    key: 'penetration',
                    title: '渗透测试重点API',
                    children: [
                      { key: 'login', title: '登录API', count: 26 },
                      { key: 'url', title: 'URL重定向API', count: 5 },
                      { key: 'response', title: '单次响应数据量过大...', count: 3 },
                      { key: 'sms', title: '短信验证码发送API', count: 2 },
                      { key: 'register', title: '注册API', count: 0 }
                    ]
                  },
                  { key: 'network', title: '互联网敏感API' },
                  { key: 'format', title: 'API格式' },
                  { key: 'label', title: 'API标签' }
                ]
            }}
          ]
        },
        { id: 'content', type: 'Container', props: { style: { flex: 1, display: 'flex', flexDirection: 'column' } },
          children: [
            { id: 'api_table', type: 'TableViewWithSearch',
              mockData: JSON.stringify([
                { id: 1, path: '/login', sensitivity: '高敏感', riskLevel: '高风险', totalVisits: '3.6千', trafficSource: '192.168.0.1', firstSeen: '2025-08-14 19:18:21' },
                { id: 2, path: '/abnormal', sensitivity: '高敏感', riskLevel: '高风险', totalVisits: '2.5千', trafficSource: '192.168.0.1', firstSeen: '2025-08-14 19:19:12' },
                { id: 3, path: '/api/users', sensitivity: '中敏感', riskLevel: '中风险', totalVisits: '1.2千', trafficSource: '192.168.0.2', firstSeen: '2025-08-14 20:15:33' },
                { id: 4, path: '/api/posts', sensitivity: '低敏感', riskLevel: '低风险', totalVisits: '800', trafficSource: '192.168.0.3', firstSeen: '2025-08-14 21:22:45' }
              ], null, 2),
              props: {
                title: '全部API',
                searchFields: [
                  { key: 'application', label: '应用', type: 'input', placeholder: '请输入' },
                  { key: 'sensitivityLevel', label: 'API敏感等级', type: 'select', placeholder: '请选择', options: [
                    { label: '高敏感', value: 'high' }, { label: '中敏感', value: 'medium' }, { label: '低敏感', value: 'low' }
                  ]},
                  { key: 'applicationName', label: '应用名称', type: 'input', placeholder: '请输入' },
                  { key: 'apiTags', label: 'API标签', type: 'combinedSelect', placeholder: '请选择', combinedConfig: {
                    leftOptions: [{ label: '或', value: 'or' }, { label: '且', value: 'and' }],
                    rightOptions: [{ label: '标签1', value: 'tag1' }, { label: '标签2', value: 'tag2' }]
                  }},
                  { key: 'accessDomain', label: '访问域', type: 'combinedSelect', placeholder: '请选择', combinedConfig: {
                    leftOptions: [{ label: '或', value: 'or' }],
                    rightOptions: [{ label: '域名1', value: 'domain1' }, { label: '域名2', value: 'domain2' }]
                  }},
                  { key: 'deployDomain', label: '部署域', type: 'combinedSelect', placeholder: '请选择', combinedConfig: {
                    leftOptions: [{ label: '或', value: 'or' }],
                    rightOptions: [{ label: '部署域1', value: 'deploy1' }, { label: '部署域2', value: 'deploy2' }]
                  }},
                  { key: 'terminalType', label: '终端类型', type: 'combinedSelect', placeholder: '请选择', combinedConfig: {
                    leftOptions: [{ label: '或', value: 'or' }],
                    rightOptions: [{ label: 'Web', value: 'web' }, { label: 'Mobile', value: 'mobile' }]
                  }},
                  { key: 'requestType', label: '请求类型', type: 'select', placeholder: '请选择', options: [
                    { label: 'GET', value: 'get' }, { label: 'POST', value: 'post' }, { label: 'PUT', value: 'put' }, { label: 'DELETE', value: 'delete' }
                  ]},
                  { key: 'responseType', label: '响应类型', type: 'select', placeholder: '请选择', options: [
                    { label: 'JSON', value: 'json' }, { label: 'XML', value: 'xml' }, { label: 'HTML', value: 'html' }
                  ]},
                  { key: 'responseDataTags', label: '响应数据标签', type: 'checkbox', placeholder: '请选择',
                    options: [{ label: '标签A', value: 'tagA' }, { label: '标签B', value: 'tagB' }],
                    checkboxConfig: { text: '按单例/桶', tooltip: '提示信息' }
                  },
                  { key: 'discoveryTime', label: '首次发现时间', type: 'select', placeholder: '请选择', options: [
                    { label: '今天', value: 'today' }, { label: '昨天', value: 'yesterday' }, { label: '最近7天', value: 'week' }, { label: '最近30天', value: 'month' }
                  ]}
                ],
                showMoreSearchConditions: true,
                quickSearch: { placeholder: '请输入URL进行筛选' },
                toolbarActions: [
                  { key: 'batch', label: '批量操作', type: 'primary' },
                  { key: 'saveView', label: '保存视图', type: 'default' }
                ],
                toolbarIcons: [
                  { key: 'filter', icon: 'filter_alt', tooltip: '筛选' },
                  { key: 'refresh', icon: 'refresh', tooltip: '刷新' },
                  { key: 'columns', icon: 'view_column', tooltip: '列设置' }
                ],
                groupTabs: [
                  { key: 'high', label: '高风险', count: 34, active: true },
                  { key: 'medium', label: '中风险', count: 38, active: false },
                  { key: 'low', label: '低风险', count: 8, active: false },
                  { key: 'none', label: '无风险', count: 124, active: false },
                  { key: 'other', label: '其他', count: 8, active: false }
                ],
                showCancelGroup: true,
                columns: [
                  { key: 'path', title: '路径', dataIndex: 'path', width: '33%' },
                  { key: 'sensitivity', title: 'API敏感等级', dataIndex: 'sensitivity', width: '16%' },
                  { key: 'riskLevel', title: 'API风险等级', dataIndex: 'riskLevel', width: '16%' },
                  { key: 'totalVisits', title: '累计访问次数', dataIndex: 'totalVisits', width: '16%', sortable: true },
                  { key: 'trafficSource', title: '流量来源', dataIndex: 'trafficSource', width: '16%' },
                  { key: 'firstSeen', title: '首次发现时间', dataIndex: 'firstSeen', width: '25%', sortable: true }
                ],
                rowActions: [
                  { key: 'copy', label: '📋', onClick: () => {} },
                  { key: 'more', label: '⋯', onClick: () => {} }
                ],
                rowSelection: { type: 'checkbox' },
                pagination: { current: 1, pageSize: 25, total: 34, showSizeChanger: true, showTotal: true }
            }}
          ]
        }
      ]
    },
    { id: 'status_bar', type: 'StatusBar', props: { items: [ { key: 'copyright', label: 'Copyright', value: '© 2017-2025 All rights reserved.' }, { key: 'version', label: '版本', value: '3.3.0.20250731' } ] } }
  ],
  apis: [],
  theme: undefined as any
};


// 设计器内部桥接：把设计器上下文中的 schema 同步回父组件
const SchemaSyncBridge: React.FC<{ onChange: (schema: PageSchema) => void }> = ({ onChange }) => {
  const { schema } = useDesigner();
  useEffect(() => {
    onChange(schema);
  }, [schema, onChange]);
  return null;
};

const App: React.FC = () => {
  // 从localStorage加载保存的schema，如果没有则使用默认的apiPageSchema
  const [currentSchema, setCurrentSchema] = useState<PageSchema>(() => {
    try {
      const savedSchema = localStorage.getItem('lowcode-designer-schema');
      if (savedSchema) {
        const parsed = JSON.parse(savedSchema);
        // 确保解析的数据有必要的字段
        if (parsed && parsed.id && parsed.components) {
          // 检查是否有mockData字段，如果没有则使用默认schema
          const hasTableComponent = parsed.components.some((comp: any) =>
            comp.type === 'TableViewWithSearch' ||
            (comp.children && comp.children.some((child: any) => child.type === 'TableViewWithSearch'))
          );

          if (hasTableComponent) {
            // 检查TableViewWithSearch组件是否有mockData
            const findTableComponent = (components: any[]): any => {
              for (const comp of components) {
                if (comp.type === 'TableViewWithSearch') {
                  return comp;
                }
                if (comp.children) {
                  const found = findTableComponent(comp.children);
                  if (found) return found;
                }
              }
              return null;
            };

            const tableComponent = findTableComponent(parsed.components);
            if (tableComponent && !tableComponent.mockData) {
              // 如果TableViewWithSearch组件没有mockData，使用默认schema
              console.log('TableViewWithSearch component missing mockData, using default schema');
              return apiPageSchema;
            }
          }

          return parsed;
        }
      }
    } catch (error) {
      console.warn('Failed to load saved schema from localStorage:', error);
    }
    return apiPageSchema;
  });

  // 当schema变化时，保存到localStorage
  const handleSchemaChange = useCallback((newSchema: PageSchema) => {
    setCurrentSchema(newSchema);
    try {
      localStorage.setItem('lowcode-designer-schema', JSON.stringify(newSchema));
    } catch (error) {
      console.warn('Failed to save schema to localStorage:', error);
    }
  }, []);




// 左侧组件面板：使用设计稿样式的侧边栏
const CollapsibleComponentPanel: React.FC = () => {
  return <DesignComponentSidebar />;
};

  return (
    <div style={{ height: '100vh', display: 'flex', flexDirection: 'column' }}>
      <DesignerProvider initialSchema={currentSchema}>
        <div style={{ flex: 1, display: 'flex', flexDirection: 'column' }}>
          <Toolbar />
          <div style={{ flex: 1, display: 'flex', position: 'relative', overflow: 'hidden' }}>
            <CollapsibleComponentPanel />
            <div style={{ flex: 1, minWidth: 0 }}>
              <Canvas />
            </div>
            {/* 右侧属性编辑以抽屉覆盖在预览区上方 */}
            <PropertyDrawer />
          </div>
        </div>
        {/* 同步设计器中的 schema 到父组件，从而影响渲染器视图 */}
        <SchemaSyncBridge onChange={handleSchemaChange} />
      </DesignerProvider>
    </div>
  );
};

export default App;
