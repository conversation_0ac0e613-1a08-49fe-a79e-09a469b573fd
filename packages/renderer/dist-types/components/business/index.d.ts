import { ComponentMeta } from '../../types';
import { TopNavigation } from './TopNavigation';
import { SidebarTreeView } from './SidebarTreeView';
import { TableViewWithSearch } from './TableViewWithSearch';
import { StatusBar } from './StatusBar';
export { TopNavigation, SidebarTreeView, TableViewWithSearch, StatusBar };
export declare const topNavigationMeta: ComponentMeta;
export declare const sidebarTreeViewMeta: ComponentMeta;
export declare const tableViewWithSearchMeta: ComponentMeta;
export declare const statusBarMeta: ComponentMeta;
export declare const businessComponentMetas: ComponentMeta[];
export declare const businessComponents: {
    TopNavigation: import("react").FC<import("./TopNavigation").TopNavigationProps>;
    SidebarTreeView: import("react").FC<import("./SidebarTreeView").SidebarTreeViewProps>;
    TableViewWithSearch: import("react").FC<import("./TableViewWithSearch").TableViewWithSearchProps>;
    StatusBar: import("react").FC<import("./StatusBar").StatusBarProps>;
};
//# sourceMappingURL=index.d.ts.map