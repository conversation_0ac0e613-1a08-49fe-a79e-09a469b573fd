import React from 'react';
import { ComponentMeta } from '../../types';
export declare const Text: React.FC<any>;
export declare const Button: React.FC<any>;
export declare const Input: React.FC<any>;
export declare const Container: React.FC<any>;
export declare const Image: React.FC<any>;
export declare const Link: React.FC<any>;
export declare const basicComponentMetas: ComponentMeta[];
export declare const basicComponents: {
    Text: React.FC<any>;
    Button: React.FC<any>;
    Input: React.FC<any>;
    Container: React.FC<any>;
    Image: React.FC<any>;
    Link: React.FC<any>;
};
//# sourceMappingURL=index.d.ts.map