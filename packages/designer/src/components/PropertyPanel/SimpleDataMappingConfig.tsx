import React from 'react';
import { Card, Form, Input, Typography } from 'antd';

const { Text } = Typography;

export interface DataMapping {
  [componentField: string]: string; // componentField -> apiField
}

interface SimpleDataMappingConfigProps {
  mappableFields: Record<string, {
    label: string;
    type: string;
    required: boolean;
    description: string;
  }>;
  value?: DataMapping;
  onChange?: (mapping: DataMapping) => void;
}

export const SimpleDataMappingConfig: React.FC<SimpleDataMappingConfigProps> = ({
  mappableFields,
  value = {},
  onChange
}) => {
  // 处理单个字段映射的变化
  const handleFieldChange = (componentField: string, apiField: string) => {
    const newMapping = { ...value };
    if (apiField.trim()) {
      newMapping[componentField] = apiField.trim();
    } else {
      delete newMapping[componentField];
    }
    onChange?.(newMapping);
  };

  return (
    <Card size="small" style={{ marginTop: 16 }}>

      <Form layout="vertical" size="small">
        {Object.entries(mappableFields).map(([fieldKey, fieldConfig]) => (
          <div key={fieldKey} style={{
            display: 'flex',
            alignItems: 'center',
            marginBottom: 12,
            gap: 8
          }}>
            <div style={{
              minWidth: 120,
              display: 'flex',
              alignItems: 'center',
              gap: 4
            }}>
              <Text code style={{ fontSize: '12px' }}>{fieldKey}</Text>
              {fieldConfig.required && <Text type="danger">*</Text>}
            </div>

            <Text style={{ fontSize: '16px', color: '#666' }}>←</Text>

            <Input
              size="small"
              value={value[fieldKey] || ''}
              onChange={(e) => handleFieldChange(fieldKey, e.target.value)}
              placeholder={fieldKey}
              style={{
                width: 120,
                fontFamily: 'monospace',
                fontSize: '12px'
              }}
            />

            <Text type="secondary" style={{ fontSize: '11px', marginLeft: 8 }}>
              ({fieldConfig.label})
            </Text>
          </div>
        ))}
      </Form>
    </Card>
  );
};
