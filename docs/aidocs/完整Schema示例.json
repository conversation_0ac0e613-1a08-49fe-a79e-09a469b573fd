{"version": "1.0.0", "meta": {"title": "用户管理系统", "description": "用户列表查询和编辑功能"}, "theme": "industryA", "layout": "single-column", "components": [{"id": "userTableWithFilter", "type": "tableWith<PERSON>ilter", "props": {"title": "用户列表", "filters": [{"name": "username", "label": "用户名", "type": "input", "placeholder": "请输入用户名"}, {"name": "email", "label": "邮箱", "type": "input", "placeholder": "请输入邮箱"}, {"name": "status", "label": "状态", "type": "select", "options": [{"label": "全部", "value": ""}, {"label": "启用", "value": "active"}, {"label": "禁用", "value": "inactive"}]}], "table": {"columns": [{"title": "ID", "dataIndex": "id", "type": "text", "width": 80}, {"title": "用户名", "dataIndex": "username", "type": "text"}, {"title": "邮箱", "dataIndex": "email", "type": "text"}, {"title": "省份", "dataIndex": "province", "type": "text"}, {"title": "城市", "dataIndex": "city", "type": "text"}, {"title": "状态", "dataIndex": "status", "type": "tag", "mapping": {"active": {"text": "启用", "color": "green"}, "inactive": {"text": "禁用", "color": "red"}}}, {"title": "操作", "type": "actions", "width": 200, "actions": [{"label": "编辑", "type": "drawer", "target": "userForm", "api": "getUserDetail"}, {"label": "删除", "type": "confirm", "api": "deleteUser", "confirmText": "确定删除该用户吗？"}]}], "pagination": true, "pageSize": 20, "showSizeChanger": true}, "toolbar": [{"label": "新增用户", "type": "drawer", "target": "userForm", "buttonType": "primary"}, {"label": "批量导入", "type": "modal", "target": "importForm"}]}, "dataBinding": {"source": "getUserList", "autoLoad": true}}, {"id": "userForm", "type": "formWithCascade", "props": {"title": "用户信息", "width": 600, "fields": [{"name": "username", "label": "用户名", "type": "input", "required": true, "validation": "^[a-zA-Z0-9_]{3,20}$", "placeholder": "请输入用户名"}, {"name": "email", "label": "邮箱", "type": "input", "required": true, "validation": "email", "placeholder": "请输入邮箱地址"}, {"name": "province", "label": "省份", "type": "select", "required": true, "placeholder": "请选择省份", "cascade": {"target": "city", "api": "getCities", "paramKey": "provinceId"}}, {"name": "city", "label": "城市", "type": "select", "required": true, "placeholder": "请选择城市", "dependsOn": "province"}, {"name": "status", "label": "状态", "type": "select", "required": true, "options": [{"label": "启用", "value": "active"}, {"label": "禁用", "value": "inactive"}]}], "layout": "vertical", "submitText": "保存", "cancelText": "取消"}, "actions": {"submit": "saveUser", "afterSubmit": "refreshTable:userTableWithFilter"}}, {"id": "importForm", "type": "form", "props": {"title": "批量导入用户", "fields": [{"name": "file", "label": "用户文件", "type": "upload", "required": true, "accept": ".xlsx,.csv", "maxSize": "5MB", "tip": "支持 Excel 和 CSV 格式，最大 5MB"}, {"name": "overwrite", "label": "覆盖已存在用户", "type": "switch", "defaultValue": false}], "submitText": "开始导入", "cancelText": "取消"}, "actions": {"submit": "importUsers", "afterSubmit": "refreshTable:userTableWithFilter"}}], "apis": {"getUserList": {"url": "/api/users", "method": "GET", "params": ["username", "email", "status", "page", "pageSize"], "responseMapping": "data"}, "getUserDetail": {"url": "/api/users/{id}", "method": "GET", "responseMapping": "data"}, "saveUser": {"url": "/api/users", "method": "POST", "successMessage": "保存成功"}, "deleteUser": {"url": "/api/users/{id}", "method": "DELETE", "successMessage": "删除成功"}, "getProvinces": {"url": "/api/regions/provinces", "method": "GET", "responseMapping": "data"}, "getCities": {"url": "/api/regions/cities", "method": "GET", "params": ["provinceId"], "responseMapping": "data"}, "importUsers": {"url": "/api/users/import", "method": "POST", "contentType": "multipart/form-data", "successMessage": "导入成功"}}}