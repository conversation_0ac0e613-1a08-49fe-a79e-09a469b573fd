import React, { useState, useEffect } from 'react';
import { Form, Select, Input, Button, Space, Typography, Divider, Tooltip, Card } from 'antd';
import { PlusOutlined, InfoCircleOutlined } from '@ant-design/icons';

const { Text, Title } = Typography;
const { Option } = Select;

export interface MappableField {
  label: string;
  type: string;
  required: boolean;
  description: string;
}

export interface DataMapping {
  [componentField: string]: string; // 组件字段 -> API响应字段
}

export interface DataMappingConfigProps {
  // 组件可映射的字段定义
  mappableFields: Record<string, MappableField>;
  // 当前的数据映射配置
  mapping: DataMapping;
  // 映射变化回调
  onMappingChange: (mapping: DataMapping) => void;
  // API响应示例数据，用于提取字段
  sampleData?: any[];
}

export const DataMappingConfig: React.FC<DataMappingConfigProps> = ({
  mappableFields,
  mapping,
  onMappingChange,
  sampleData = []
}) => {
  const [form] = Form.useForm();
  const [apiFields, setApiFields] = useState<string[]>([]);

  // 从示例数据中提取API字段
  useEffect(() => {
    if (sampleData && sampleData.length > 0) {
      const fields = new Set<string>();
      
      const extractFields = (obj: any, prefix = '') => {
        if (obj && typeof obj === 'object') {
          Object.keys(obj).forEach(key => {
            const fullKey = prefix ? `${prefix}.${key}` : key;
            fields.add(fullKey);
            
            // 递归处理嵌套对象
            if (typeof obj[key] === 'object' && obj[key] !== null && !Array.isArray(obj[key])) {
              extractFields(obj[key], fullKey);
            }
          });
        }
      };

      sampleData.forEach(item => extractFields(item));
      setApiFields(Array.from(fields).sort());
    }
  }, [sampleData]);

  // 初始化表单值
  useEffect(() => {
    form.setFieldsValue(mapping);
  }, [mapping, form]);

  // 处理映射变化
  const handleMappingChange = (_changedValues: any, allValues: any) => {
    onMappingChange(allValues);
  };

  // 添加自定义API字段
  const [customField, setCustomField] = useState('');
  const handleAddCustomField = () => {
    if (customField && !apiFields.includes(customField)) {
      setApiFields([...apiFields, customField].sort());
      setCustomField('');
    }
  };

  return (
    <div style={{ padding: '16px' }}>
      <Title level={5}>数据映射配置</Title>
      <Text type="secondary" style={{ marginBottom: '16px', display: 'block' }}>
        配置API响应数据字段与组件属性的映射关系
      </Text>

      <Form
        form={form}
        layout="vertical"
        onValuesChange={handleMappingChange}
        initialValues={mapping}
      >
        {Object.entries(mappableFields).map(([fieldKey, fieldConfig]) => (
          <Form.Item
            key={fieldKey}
            name={fieldKey}
            label={
              <Space>
                <Text strong>{fieldConfig.label}</Text>
                {fieldConfig.required && <Text type="danger">*</Text>}
                <Tooltip title={fieldConfig.description}>
                  <InfoCircleOutlined style={{ color: '#999' }} />
                </Tooltip>
              </Space>
            }
            rules={[
              {
                required: fieldConfig.required,
                message: `请选择${fieldConfig.label}对应的API字段`
              }
            ]}
          >
            <Select
              placeholder={`选择API字段映射到 ${fieldConfig.label}`}
              allowClear
              showSearch
              filterOption={(input, option) =>
                String(option?.children || '').toLowerCase().includes(input.toLowerCase())
              }
            >
              {apiFields.map(field => (
                <Option key={field} value={field}>
                  {field}
                </Option>
              ))}
            </Select>
          </Form.Item>
        ))}
      </Form>

      <Divider />

      {/* 自定义API字段添加 */}
      <Card size="small" title="添加自定义API字段" style={{ marginTop: '16px' }}>
        <Space.Compact style={{ width: '100%' }}>
          <Input
            placeholder="输入API字段名，如: data.items[0].name"
            value={customField}
            onChange={(e) => setCustomField(e.target.value)}
            onPressEnter={handleAddCustomField}
          />
          <Button 
            type="primary" 
            icon={<PlusOutlined />}
            onClick={handleAddCustomField}
            disabled={!customField}
          >
            添加
          </Button>
        </Space.Compact>
        <Text type="secondary" style={{ fontSize: '12px', marginTop: '8px', display: 'block' }}>
          支持嵌套字段，如：user.profile.name 或 items[0].title
        </Text>
      </Card>

      {/* 当前映射预览 */}
      {Object.keys(mapping).length > 0 && (
        <Card size="small" title="当前映射关系" style={{ marginTop: '16px' }}>
          {Object.entries(mapping).map(([componentField, apiField]) => (
            <div key={componentField} style={{ marginBottom: '4px' }}>
              <Text code>{componentField}</Text>
              <Text style={{ margin: '0 8px' }}>←</Text>
              <Text type="secondary">{apiField}</Text>
            </div>
          ))}
        </Card>
      )}
    </div>
  );
};
