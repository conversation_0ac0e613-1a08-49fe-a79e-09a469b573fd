# 渲染器核心架构

## 1. 核心模块

### 1.1 组件注册器 (ComponentRegistry)
```typescript
class ComponentRegistry {
  private components: Map<string, React.ComponentType> = new Map();
  
  register(type: string, component: React.ComponentType) {
    this.components.set(type, component);
  }
  
  get(type: string): React.ComponentType | undefined {
    return this.components.get(type);
  }
}
```

### 1.2 API 管理器 (ApiManager)
```typescript
class ApiManager {
  private baseURL: string;
  private apis: Map<string, ApiConfig> = new Map();
  
  async call(apiName: string, params?: any): Promise<any> {
    const config = this.apis.get(apiName);
    if (!config) throw new Error(`API ${apiName} not found`);
    
    // 处理 URL 参数替换
    let url = config.url;
    if (params) {
      url = this.replaceUrlParams(url, params);
    }
    
    // 发起请求
    const response = await axios({
      method: config.method,
      url: this.baseURL + url,
      data: params,
      params: config.method === 'GET' ? params : undefined
    });
    
    // 数据映射
    return this.mapResponse(response.data, config.responseMapping);
  }
}
```

### 1.3 主题管理器 (ThemeManager)
```typescript
class ThemeManager {
  private themes: Map<string, ThemeConfig> = new Map();
  private currentTheme: string = 'default';
  
  setTheme(themeName: string) {
    this.currentTheme = themeName;
    this.applyTheme();
  }
  
  private applyTheme() {
    const theme = this.themes.get(this.currentTheme);
    if (theme) {
      // 动态注入 CSS 变量
      const root = document.documentElement;
      Object.entries(theme).forEach(([key, value]) => {
        root.style.setProperty(`--${key}`, value);
      });
    }
  }
}
```

## 2. 核心组件实现

### 2.1 TableWithFilter 组件
```typescript
const TableWithFilter: React.FC<TableWithFilterProps> = ({ 
  filters, 
  table, 
  toolbar,
  dataBinding 
}) => {
  const [filterValues, setFilterValues] = useState({});
  const [tableData, setTableData] = useState([]);
  const [loading, setLoading] = useState(false);
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: table.pageSize || 20,
    total: 0
  });
  
  // 加载数据
  const loadData = useCallback(async () => {
    setLoading(true);
    try {
      const params = {
        ...filterValues,
        page: pagination.current,
        pageSize: pagination.pageSize
      };
      
      const response = await apiManager.call(dataBinding.source, params);
      setTableData(response.list);
      setPagination(prev => ({ ...prev, total: response.total }));
    } catch (error) {
      message.error('数据加载失败');
    } finally {
      setLoading(false);
    }
  }, [filterValues, pagination.current, pagination.pageSize]);
  
  // 筛选变化
  const handleFilterChange = (values: any) => {
    setFilterValues(values);
    setPagination(prev => ({ ...prev, current: 1 }));
  };
  
  // 分页变化
  const handleTableChange = (paginationConfig: any) => {
    setPagination(prev => ({
      ...prev,
      current: paginationConfig.current,
      pageSize: paginationConfig.pageSize
    }));
  };
  
  useEffect(() => {
    loadData();
  }, [loadData]);
  
  return (
    <div className="table-with-filter">
      {/* 筛选区域 */}
      <FilterForm 
        filters={filters} 
        onValuesChange={handleFilterChange}
      />
      
      {/* 工具栏 */}
      {toolbar && (
        <div className="toolbar">
          {toolbar.map(action => (
            <ActionButton key={action.label} action={action} />
          ))}
        </div>
      )}
      
      {/* 表格 */}
      <Table
        columns={table.columns}
        dataSource={tableData}
        loading={loading}
        pagination={pagination}
        onChange={handleTableChange}
        rowKey="id"
      />
    </div>
  );
};
```

### 2.2 FormWithCascade 组件
```typescript
const FormWithCascade: React.FC<FormWithCascadeProps> = ({ 
  fields, 
  actions 
}) => {
  const [form] = Form.useForm();
  const [cascadeOptions, setCascadeOptions] = useState<Record<string, any[]>>({});
  
  // 处理级联变化
  const handleCascadeChange = async (fieldName: string, value: any) => {
    const field = fields.find(f => f.name === fieldName);
    if (field?.cascade) {
      const { target, api, paramKey } = field.cascade;
      
      try {
        const options = await apiManager.call(api, { [paramKey]: value });
        setCascadeOptions(prev => ({ ...prev, [target]: options }));
        
        // 清空依赖字段的值
        form.setFieldsValue({ [target]: undefined });
      } catch (error) {
        message.error('加载选项失败');
      }
    }
  };
  
  // 提交表单
  const handleSubmit = async (values: any) => {
    try {
      await apiManager.call(actions.submit, values);
      message.success('保存成功');
      
      // 执行后续动作
      if (actions.afterSubmit) {
        const [action, target] = actions.afterSubmit.split(':');
        if (action === 'refreshTable') {
          // 触发表格刷新
          eventBus.emit('refreshTable', target);
        }
      }
    } catch (error) {
      message.error('保存失败');
    }
  };
  
  return (
    <Form form={form} onFinish={handleSubmit} layout="vertical">
      {fields.map(field => (
        <FormField
          key={field.name}
          field={field}
          options={cascadeOptions[field.name]}
          onCascadeChange={handleCascadeChange}
        />
      ))}
      
      <Form.Item>
        <Button type="primary" htmlType="submit">
          保存
        </Button>
        <Button style={{ marginLeft: 8 }}>
          取消
        </Button>
      </Form.Item>
    </Form>
  );
};
```

## 3. 事件系统

```typescript
class EventBus {
  private events: Map<string, Function[]> = new Map();
  
  on(event: string, callback: Function) {
    if (!this.events.has(event)) {
      this.events.set(event, []);
    }
    this.events.get(event)!.push(callback);
  }
  
  emit(event: string, ...args: any[]) {
    const callbacks = this.events.get(event);
    if (callbacks) {
      callbacks.forEach(callback => callback(...args));
    }
  }
  
  off(event: string, callback: Function) {
    const callbacks = this.events.get(event);
    if (callbacks) {
      const index = callbacks.indexOf(callback);
      if (index > -1) {
        callbacks.splice(index, 1);
      }
    }
  }
}

export const eventBus = new EventBus();
```

## 4. 渲染器主入口

```typescript
const Renderer: React.FC<{ schema: Schema }> = ({ schema }) => {
  const { theme, components, apis } = schema;
  
  useEffect(() => {
    // 设置主题
    themeManager.setTheme(theme);
    
    // 注册 APIs
    Object.entries(apis).forEach(([name, config]) => {
      apiManager.register(name, config);
    });
  }, [theme, apis]);
  
  return (
    <div className="lowcode-renderer">
      {components.map(component => {
        const Component = componentRegistry.get(component.type);
        if (!Component) {
          console.warn(`Component type ${component.type} not found`);
          return null;
        }
        
        return (
          <Component
            key={component.id}
            {...component.props}
            dataBinding={component.dataBinding}
            actions={component.actions}
          />
        );
      })}
    </div>
  );
};
```
