import React, { useState } from 'react';
import { useDesigner } from '../../context/DesignerContext';

export interface ToolbarProps {
  height?: number;
  style?: React.CSSProperties;
  className?: string;
}

export const Toolbar: React.FC<ToolbarProps> = ({ height = 48, style, className }) => {
  const { schema, canvasState, toggleComponentPanel, updateSchema } = useDesigner();
  const [isEditingTitle, setIsEditingTitle] = useState(false);
  const [editTitle, setEditTitle] = useState('');

  const defaultStyle: React.CSSProperties = {
    height,
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: '0 24px',
    background: '#fff',
    borderBottom: '1px solid #e8e8e8',
    ...style
  };

  const openPreview = () => {
    // 将schema数据存储到sessionStorage
    sessionStorage.setItem('lowcode-preview-schema', JSON.stringify(schema));

    // 打开新窗口到预览页面
    const previewUrl = `${window.location.origin}${window.location.pathname}?mode=preview`;
    window.open(previewUrl, '_blank', 'width=1200,height=800');
  };

  const titleStyle: React.CSSProperties = {
    fontSize: '20px',
    fontWeight: '700',
    color: '#1f2937',
    margin: 0
  };

  const buttonStyle: React.CSSProperties = {
    backgroundColor: '#2563eb',
    color: '#ffffff',
    fontWeight: '600',
    padding: '8px 16px',
    borderRadius: '6px',
    border: 'none',
    fontSize: '14px',
    cursor: 'pointer',
    transition: 'background-color 0.2s ease'
  };

  const buttonHoverStyle: React.CSSProperties = {
    backgroundColor: '#1d4ed8'
  };

  // 处理标题编辑
  const handleEditTitle = () => {
    setEditTitle(schema.title || '');
    setIsEditingTitle(true);
  };

  const handleSaveTitle = () => {
    if (editTitle.trim()) {
      updateSchema({
        ...schema,
        title: editTitle.trim()
      });
    }
    setIsEditingTitle(false);
  };

  const handleCancelEdit = () => {
    setIsEditingTitle(false);
    setEditTitle('');
  };

  // 处理重置页面
  const handleResetPage = () => {
    if (confirm('确定要重置页面到初始状态吗？这将清除所有修改。')) {
      try {
        localStorage.removeItem('lowcode-designer-schema');
        window.location.reload();
      } catch (error) {
        console.warn('Failed to reset page:', error);
      }
    }
  };

  return (
    <div className={`lowcode-toolbar ${className || ''}`} style={defaultStyle}>
      {/* 左侧：组件库切换按钮 + 页面标题 */}
      <div style={{ display: 'flex', alignItems: 'center', gap: '16px' }}>
        <button
          onClick={toggleComponentPanel}
          style={{
            padding: '8px',
            backgroundColor: 'transparent',
            border: '1px solid #d9d9d9',
            borderRadius: '6px',
            cursor: 'pointer',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            transition: 'all 0.2s ease',
            width: '32px',
            height: '32px'
          }}
          onMouseEnter={(e) => {
            e.currentTarget.style.backgroundColor = '#f5f5f5';
            e.currentTarget.style.borderColor = '#1890ff';
          }}
          onMouseLeave={(e) => {
            e.currentTarget.style.backgroundColor = 'transparent';
            e.currentTarget.style.borderColor = '#d9d9d9';
          }}
          title={canvasState.componentPanelCollapsed ? '展开组件库' : '收缩组件库'}
        >
          <span
            className="material-icons-outlined"
            style={{
              fontSize: '18px',
              color: '#666',
              transition: 'transform 0.2s ease'
            }}
          >
            {canvasState.componentPanelCollapsed ? 'menu_open' : 'menu'}
          </span>
        </button>

        <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
          <h1 style={titleStyle}>
            {schema.title || '低代码平台示例'}
          </h1>
          <button
            onClick={handleEditTitle}
            style={{
              padding: '4px',
              backgroundColor: 'transparent',
              border: 'none',
              borderRadius: '4px',
              cursor: 'pointer',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              transition: 'all 0.2s ease',
              opacity: 0.6
            }}
            onMouseEnter={(e) => {
              e.currentTarget.style.backgroundColor = '#f5f5f5';
              e.currentTarget.style.opacity = '1';
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.backgroundColor = 'transparent';
              e.currentTarget.style.opacity = '0.6';
            }}
            title="编辑页面标题"
          >
            <span
              className="material-icons-outlined"
              style={{
                fontSize: '18px',
                color: '#666'
              }}
            >
              edit
            </span>
          </button>
        </div>
      </div>

      {/* 右侧：操作按钮 */}
      <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
        <button
          onClick={handleResetPage}
          style={{
            padding: '8px 16px',
            backgroundColor: '#ffffff',
            color: '#666666',
            border: '1px solid #d1d5db',
            borderRadius: '6px',
            cursor: 'pointer',
            fontSize: '14px',
            fontWeight: '500',
            transition: 'all 0.2s ease'
          }}
          onMouseEnter={(e) => {
            e.currentTarget.style.backgroundColor = '#f9fafb';
            e.currentTarget.style.borderColor = '#ff4d4f';
            e.currentTarget.style.color = '#ff4d4f';
          }}
          onMouseLeave={(e) => {
            e.currentTarget.style.backgroundColor = '#ffffff';
            e.currentTarget.style.borderColor = '#d1d5db';
            e.currentTarget.style.color = '#666666';
          }}
          title="重置页面到初始状态"
        >
          重置
        </button>

        <button
          style={buttonStyle}
          onMouseEnter={(e) => Object.assign(e.currentTarget.style, buttonHoverStyle)}
          onMouseLeave={(e) => Object.assign(e.currentTarget.style, buttonStyle)}
          onClick={openPreview}
        >
          预览
        </button>
      </div>

      {/* 编辑标题弹窗 */}
      {isEditingTitle && (
        <div
          style={{
            position: 'fixed',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            backgroundColor: 'rgba(0, 0, 0, 0.5)',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            zIndex: 10000
          }}
          onClick={handleCancelEdit}
        >
          <div
            style={{
              backgroundColor: '#ffffff',
              borderRadius: '8px',
              padding: '24px',
              minWidth: '400px',
              boxShadow: '0 4px 20px rgba(0, 0, 0, 0.15)',
              position: 'relative'
            }}
            onClick={(e) => e.stopPropagation()}
          >
            <h3 style={{
              margin: '0 0 16px 0',
              fontSize: '18px',
              fontWeight: '600',
              color: '#1f2937'
            }}>
              编辑页面标题
            </h3>

            <input
              type="text"
              value={editTitle}
              onChange={(e) => setEditTitle(e.target.value)}
              placeholder="请输入页面标题"
              style={{
                width: '100%',
                padding: '12px',
                border: '1px solid #d1d5db',
                borderRadius: '6px',
                fontSize: '14px',
                outline: 'none',
                marginBottom: '20px',
                boxSizing: 'border-box'
              }}
              onFocus={(e) => {
                e.currentTarget.style.borderColor = '#1890ff';
                e.currentTarget.style.boxShadow = '0 0 0 3px rgba(24, 144, 255, 0.1)';
              }}
              onBlur={(e) => {
                e.currentTarget.style.borderColor = '#d1d5db';
                e.currentTarget.style.boxShadow = 'none';
              }}
              onKeyDown={(e) => {
                if (e.key === 'Enter') {
                  handleSaveTitle();
                } else if (e.key === 'Escape') {
                  handleCancelEdit();
                }
              }}
              autoFocus
            />

            <div style={{
              display: 'flex',
              justifyContent: 'flex-end',
              gap: '12px'
            }}>
              <button
                onClick={handleCancelEdit}
                style={{
                  padding: '8px 16px',
                  backgroundColor: '#ffffff',
                  color: '#666666',
                  border: '1px solid #d1d5db',
                  borderRadius: '6px',
                  cursor: 'pointer',
                  fontSize: '14px',
                  transition: 'all 0.2s ease'
                }}
                onMouseEnter={(e) => {
                  e.currentTarget.style.backgroundColor = '#f9fafb';
                }}
                onMouseLeave={(e) => {
                  e.currentTarget.style.backgroundColor = '#ffffff';
                }}
              >
                取消
              </button>
              <button
                onClick={handleSaveTitle}
                style={{
                  padding: '8px 16px',
                  backgroundColor: '#1890ff',
                  color: '#ffffff',
                  border: 'none',
                  borderRadius: '6px',
                  cursor: 'pointer',
                  fontSize: '14px',
                  fontWeight: '500',
                  transition: 'background-color 0.2s ease'
                }}
                onMouseEnter={(e) => {
                  e.currentTarget.style.backgroundColor = '#40a9ff';
                }}
                onMouseLeave={(e) => {
                  e.currentTarget.style.backgroundColor = '#1890ff';
                }}
              >
                保存
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

