# 低代码平台架构重构方案

## 1. 问题分析

### 1.1 当前架构问题
当前项目将"低代码开发平台"和"低代码运行平台"混合在一起，导致概念模糊：

1. **职责混乱**：
   - @lowcode/renderer 既是渲染引擎又包含业务组件
   - @lowcode/designer 依赖 @lowcode/renderer，形成概念上的循环依赖
   - examples/simple-app 同时承担设计器和预览功能

2. **部署困惑**：
   - 设计器应该是开发工具，不应该包含运行时代码
   - 运行平台应该是独立的，只需要渲染器+JSON Schema
   - 当前架构无法清晰地分离开发环境和生产环境

3. **扩展性问题**：
   - 业务组件与渲染引擎耦合
   - 无法独立升级组件库
   - 难以支持多租户和定制化需求

## 2. 目标架构

### 2.1 两个独立平台的正确理解
```
┌─────────────────────────────────┐    ┌─────────────────────────────────┐
│        低代码开发平台              │    │        低代码运行平台              │
│    (LowCode Development)        │    │     (LowCode Runtime)          │
├─────────────────────────────────┤    ├─────────────────────────────────┤
│ • 可视化设计器                   │    │ • 渲染引擎                      │
│ • 组件面板 (预览用)              │────▶│ • JSON Schema解析              │
│ • 属性配置面板                   │    │ • 业务组件库 (完整功能)          │
│ • 实时预览 (需要组件库)           │    │ • 主题系统 (生产样式)            │
│ • API配置 (模拟数据)             │    │ • API管理 (真实请求)            │
│ • Schema导出                    │    │ • 事件处理                      │
└─────────────────────────────────┘    └─────────────────────────────────┘
            ↓                                        ↑
    JSON Schema 文件                          JSON Schema 文件

        共享相同的组件库、主题系统、API管理
        ┌─────────────────────────────────┐
        │         共享基础设施              │
        ├─────────────────────────────────┤
        │ • 业务组件库 (TopNavigation等)   │
        │ • 主题系统 (样式定义)            │
        │ • API管理器 (请求处理)           │
        │ • 事件系统 (组件通信)            │
        │ • 类型定义 (Schema等)            │
        └─────────────────────────────────┘
```

### 2.2 核心原则（修正）
1. **开发平台**：可视化设计 + 实时预览，产出JSON Schema
2. **运行平台**：生产环境渲染，消费JSON Schema
3. **共享基础设施**：组件库、主题、API管理等两个平台都需要
4. **使用方式不同**：开发时用于配置预览，运行时用于实际功能

## 3. 重构方案

### 3.1 新的项目结构（基于正确理解）
```
lowcode-platform/
├── packages/
│   ├── shared/                 # 共享基础设施
│   │   ├── types/              # 共享类型定义 (Schema等)
│   │   ├── utils/              # 共享工具函数
│   │   ├── components/         # 业务组件库 (两个平台共享)
│   │   │   ├── basic/          # 基础组件
│   │   │   ├── business/       # 业务组件 (TopNavigation等)
│   │   │   └── layout/         # 布局组件
│   │   ├── themes/             # 主题系统 (两个平台共享)
│   │   ├── api/                # API管理器 (两个平台共享)
│   │   └── events/             # 事件系统 (两个平台共享)
│   │
│   ├── renderer/               # 渲染引擎 (运行平台核心)
│   │   ├── core/               # Schema解析和渲染
│   │   ├── runtime/            # 运行时优化
│   │   └── context/            # React Context
│   │
│   └── designer/               # 设计器 (开发平台核心)
│       ├── canvas/             # 可视化画布
│       ├── panels/             # 组件面板、属性面板等
│       ├── preview/            # 实时预览 (使用shared/components)
│       ├── toolbar/            # 工具栏
│       └── context/            # 设计器状态管理
│
├── apps/
│   ├── lowcode-designer/       # 低代码开发平台
│   │   ├── src/
│   │   │   ├── App.tsx         # 设计器主应用
│   │   │   ├── components/     # 设计器专用UI
│   │   │   └── utils/          # 设计器工具
│   │   ├── public/
│   │   └── package.json
│   │
│   ├── lowcode-runtime/        # 低代码运行平台
│   │   ├── src/
│   │   │   ├── App.tsx         # 运行时主应用
│   │   │   ├── loader/         # Schema加载器
│   │   │   └── utils/          # 运行时工具
│   │   ├── public/
│   │   └── package.json
│   │
│   └── component-storybook/    # 组件文档和预览
│       ├── src/
│       └── package.json
│
├── examples/                   # 示例和文档
│   ├── schemas/                # 示例Schema文件
│   ├── custom-components/      # 自定义组件示例
│   └── integration/            # 集成示例
│
└── docs/                       # 文档
    ├── architecture/           # 架构文档
    ├── development/            # 开发文档
    ├── deployment/             # 部署文档
    └── api/                    # API文档
```

### 3.2 包依赖关系（修正）
```
@lowcode/shared (共享基础设施)
├── types (类型定义)
├── components (业务组件库)
├── themes (主题系统)
├── api (API管理)
└── events (事件系统)
    ↑                    ↑
    │                    │
@lowcode/renderer    @lowcode/designer
(运行平台核心)        (开发平台核心)
    ↑                    ↑
    │                    │
lowcode-runtime      lowcode-designer
(运行平台应用)        (开发平台应用)
```

### 3.3 关键设计原则
1. **共享基础设施**：组件库、主题、API管理等被两个平台共享
2. **不同使用模式**：
   - 开发平台：组件用于预览，API用于模拟数据
   - 运行平台：组件用于实际功能，API用于真实请求
3. **独立部署**：两个应用可以独立部署和扩展
4. **一致体验**：开发时预览效果与运行时完全一致

## 4. 架构概念澄清

### 4.1 开发平台 vs 运行平台的详细对比

| 维度 | 低代码开发平台 | 低代码运行平台 |
|------|---------------|---------------|
| **主要用户** | 产品经理、开发者 | 最终用户 |
| **核心功能** | 可视化设计、配置、预览 | 页面渲染、业务处理 |
| **组件使用** | 预览效果、配置界面 | 完整业务功能 |
| **API使用** | 配置数据源、模拟数据 | 真实业务请求 |
| **主题使用** | 预览样式效果 | 生产环境样式 |
| **输出产物** | JSON Schema文件 | 渲染的页面 |
| **部署环境** | 内部开发环境 | 生产环境 |

### 4.2 共享组件的不同使用模式

#### 4.2.1 TopNavigation组件示例
**开发平台中的使用**：
- 在组件面板中展示为可拖拽项
- 在画布中显示预览效果
- 在属性面板中配置logo、菜单项等
- 支持实时预览配置变化

**运行平台中的使用**：
- 根据Schema配置渲染导航栏
- 处理真实的菜单点击事件
- 显示真实的用户信息
- 执行实际的路由跳转

#### 4.2.2 API管理器示例
**开发平台中的使用**：
- 配置API端点和参数
- 提供模拟数据用于预览
- 验证API配置的正确性
- 支持API配置的可视化编辑

**运行平台中的使用**：
- 执行真实的HTTP请求
- 处理响应数据和错误
- 管理请求状态和缓存
- 支持数据绑定和更新

### 4.3 架构优势

#### 4.3.1 一致性保证
- 开发时预览 = 运行时效果
- 相同的组件库确保视觉一致性
- 相同的API管理确保数据处理一致性

#### 4.3.2 开发效率
- 实时预览减少开发调试时间
- 组件复用降低开发成本
- 可视化配置提升产品经理效率

#### 4.3.3 维护便利
- 组件库统一维护和升级
- 主题系统集中管理
- API配置标准化

## 5. 分阶段实施

### 5.1 第一阶段：创建共享基础设施
1. 创建 @lowcode/shared 包
   - 迁移类型定义 (Schema、ComponentMeta等)
   - 迁移工具函数
   - 创建Schema验证工具

2. 重组业务组件库
   - 从renderer中提取所有业务组件
   - 保持组件功能完整性
   - 确保开发和运行时都能正常使用

3. 独立主题系统和API管理
   - 主题系统支持开发预览和生产渲染
   - API管理支持模拟数据和真实请求
   - 事件系统支持设计时和运行时

### 5.2 第二阶段：重构核心引擎
1. 精简 @lowcode/renderer
   - 专注于Schema解析和渲染逻辑
   - 依赖shared包获取组件和主题
   - 优化运行时性能

2. 重构 @lowcode/designer
   - 专注于可视化设计功能
   - 依赖shared包进行实时预览
   - 实现与运行时一致的预览效果
   - 优化设计器交互体验

### 5.3 第三阶段：创建独立应用
1. 创建 lowcode-designer 应用
   - 完整的低代码开发平台
   - 集成设计器、预览、导出功能
   - 支持Schema管理和版本控制

2. 创建 lowcode-runtime 应用
   - 完整的低代码运行平台
   - 支持Schema导入和渲染
   - 生产环境性能优化
   - 支持多租户和权限管理

### 4.4 第四阶段：优化和扩展
1. 性能优化
   - 组件懒加载
   - Schema缓存
   - 渲染优化

2. 扩展功能
   - 插件系统
   - 自定义组件支持
   - 多租户支持

## 5. 迁移策略

### 5.1 向后兼容
- 保持现有API不变
- 提供迁移工具
- 渐进式升级路径

### 5.2 数据迁移
- Schema格式保持兼容
- 组件配置自动迁移
- 提供验证工具

### 5.3 部署策略
- 支持独立部署
- 支持容器化部署
- 提供部署模板

## 6. 预期收益

### 6.1 架构清晰
- 职责明确，边界清晰
- 易于理解和维护
- 支持团队协作

### 6.2 性能提升
- 按需加载
- 独立优化
- 更好的缓存策略

### 6.3 扩展性强
- 组件库独立升级
- 支持插件扩展
- 易于定制化

### 6.4 部署灵活
- 开发和生产环境分离
- 支持多种部署方式
- 更好的安全性

## 7. 风险评估

### 7.1 技术风险
- 重构工作量较大
- 可能引入新的bug
- 需要充分测试

### 7.2 业务风险
- 短期内开发效率可能下降
- 需要团队学习新架构
- 可能影响现有项目

### 7.3 缓解措施
- 分阶段实施，降低风险
- 保持向后兼容
- 充分的测试和文档
- 团队培训和支持

## 8. 时间规划

- **第一阶段**：2-3周（拆分核心包）
- **第二阶段**：3-4周（重构渲染器和设计器）
- **第三阶段**：2-3周（创建独立应用）
- **第四阶段**：4-6周（优化和扩展）

**总计**：11-16周

## 9. 下一步行动

1. **确认方案**：团队review和确认重构方案
2. **详细设计**：制定详细的技术设计文档
3. **创建分支**：创建重构专用分支
4. **开始实施**：按阶段开始重构工作
5. **持续集成**：建立CI/CD流程确保质量
