import React from 'react';
import { ComponentSchema, PageSchema, RendererConfig, ComponentMeta } from '../types';
import { ComponentRegistry } from './ComponentRegistry';
import { ApiManager } from './ApiManager';
import { EventBus } from './EventBus';
import { ThemeManager } from './ThemeManager';
export declare class Renderer {
    private componentRegistry;
    private apiManager;
    private eventBus;
    private themeManager;
    constructor(config?: RendererConfig);
    registerComponent(meta: ComponentMeta, component: React.ComponentType<any>): void;
    renderPage(schema: PageSchema): React.ReactElement;
    renderComponent(schema: ComponentSchema, keyPrefix?: string): React.ReactElement;
    private processProps;
    private createEventHandler;
    private registerDefaultComponents;
    getComponentRegistry(): ComponentRegistry;
    getApiManager(): ApiManager;
    getEventBus(): EventBus;
    getThemeManager(): ThemeManager;
    private validatePageSchema;
    private validateComponentSchema;
    private renderUnknownComponent;
    private renderComponentError;
    private renderErrorBoundary;
    private buildClassName;
    private buildStyle;
    getStats(): {
        registeredComponents: number;
        registeredApis: number;
        eventListeners: number;
        currentTheme: import("..").ThemeConfig;
    };
    destroy(): void;
}
//# sourceMappingURL=Renderer.d.ts.map