import React from 'react';
import ReactDOM from 'react-dom/client';
import App from './App';
import Preview from './Preview';

// 根据URL参数决定渲染哪个页面
const urlParams = new URLSearchParams(window.location.search);
const isPreview = urlParams.get('mode') === 'preview';

ReactDOM.createRoot(document.getElementById('root')!).render(
  <React.StrictMode>
    {isPreview ? <Preview /> : <App />}
  </React.StrictMode>
);
