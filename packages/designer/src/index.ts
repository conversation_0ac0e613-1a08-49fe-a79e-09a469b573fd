// 导出类型定义
export * from './types';

// 导出组件
export { ComponentPanel } from './components/ComponentPanel/ComponentPanel';
export { DesignComponentSidebar } from './components/ComponentPanel/DesignComponentSidebar';
export { Canvas } from './components/Canvas/Canvas';
export { PropertyPanel } from './components/PropertyPanel/PropertyPanel';
export { ApiPanel } from './components/PropertyPanel/ApiPanel';
export { PropertyDrawer } from './components/PropertyPanel/PropertyDrawer';
export { Toolbar } from './components/Toolbar/Toolbar';
export { DesignerProvider, useDesigner } from './context/DesignerContext';

// 版本信息
export const version = '1.0.0';

// 创建设计器实例的工厂函数（占位）
export const createDesigner = (config?: import('./types').DesignerConfig) => {
  console.log('Designer factory (placeholder)', config);
};
