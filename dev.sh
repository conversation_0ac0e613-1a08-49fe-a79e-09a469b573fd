#!/bin/bash

# 低代码平台开发脚本
# 快速重启开发环境

set -e

echo "🔄 重启低代码平台开发环境..."
echo "================================"

# 颜色定义
GREEN='\033[0;32m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

# 停止现有服务
log_info "停止现有服务..."
./stop.sh

echo ""
log_info "等待 3 秒后重新启动..."
sleep 3

# 重新启动
log_info "重新启动服务..."
./start.sh

log_success "开发环境重启完成！"
