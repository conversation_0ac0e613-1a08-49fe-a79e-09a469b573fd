# 设计器功能规划

## 1. 整体布局

```
┌─────────────────────────────────────────────────────────────┐
│                        顶部工具栏                            │
├─────────────┬─────────────────────────┬─────────────────────┤
│             │                         │                     │
│   组件面板   │        画布区域          │     属性配置面板     │
│             │                         │                     │
│   - 表格     │   ┌─────────────────┐   │   ┌─────────────┐   │
│   - 表单     │   │                 │   │   │             │   │
│   - 筛选     │   │   预览/编辑区    │   │   │  字段配置   │   │
│             │   │                 │   │   │             │   │
│             │   └─────────────────┘   │   │  API配置    │   │
│             │                         │   │             │   │
│             │                         │   │  样式配置   │   │
│             │                         │   │             │   │
│             │                         │   └─────────────┘   │
├─────────────┴─────────────────────────┴─────────────────────┤
│                      底部状态栏                              │
└─────────────────────────────────────────────────────────────┘
```

## 2. 核心功能模块

### 2.1 组件面板 (ComponentPanel)
- **表格组件**：拖拽生成基础表格
- **表格筛选组件**：表格 + 筛选条件
- **表单组件**：基础表单
- **联动表单组件**：支持级联的表单

### 2.2 画布区域 (Canvas)
- **拖拽接收**：接收组件面板的拖拽
- **组件选择**：点击选择组件，高亮显示
- **实时预览**：所见即所得的预览效果
- **组件操作**：删除、复制、移动组件

### 2.3 属性配置面板 (PropertyPanel)

#### 表格配置
```typescript
interface TableConfig {
  // 基础配置
  title: string;
  pageSize: number;
  showPagination: boolean;
  
  // 列配置
  columns: ColumnConfig[];
  
  // 数据源配置
  dataSource: {
    api: string;
    autoLoad: boolean;
  };
  
  // 操作配置
  actions: ActionConfig[];
}

interface ColumnConfig {
  title: string;
  dataIndex: string;
  type: 'text' | 'tag' | 'date' | 'number';
  width?: number;
  sortable?: boolean;
  filterable?: boolean;
}
```

#### 表单配置
```typescript
interface FormConfig {
  title: string;
  layout: 'horizontal' | 'vertical';
  
  // 字段配置
  fields: FieldConfig[];
  
  // 提交配置
  submitApi: string;
  submitText: string;
  
  // 验证配置
  validation: ValidationConfig;
}

interface FieldConfig {
  name: string;
  label: string;
  type: 'input' | 'select' | 'upload' | 'date';
  required: boolean;
  placeholder?: string;
  
  // 级联配置
  cascade?: {
    target: string;
    api: string;
    paramKey: string;
  };
}
```

### 2.4 API 配置面板 (ApiPanel)
```typescript
interface ApiConfig {
  name: string;
  url: string;
  method: 'GET' | 'POST' | 'PUT' | 'DELETE';
  params: string[];
  headers?: Record<string, string>;
  responseMapping?: string;
  
  // 测试功能
  testParams?: Record<string, any>;
  testResult?: any;
}
```

## 3. 设计器核心逻辑

### 3.1 状态管理
```typescript
interface DesignerState {
  // 当前 Schema
  schema: Schema;
  
  // 选中的组件
  selectedComponent: string | null;
  
  // 组件列表
  components: ComponentInstance[];
  
  // API 配置
  apis: Record<string, ApiConfig>;
  
  // 主题配置
  theme: string;
  
  // 预览模式
  previewMode: boolean;
}
```

### 3.2 拖拽逻辑
```typescript
const handleDrop = (item: DragItem, monitor: DropTargetMonitor) => {
  const componentType = item.type;
  const dropPosition = monitor.getClientOffset();
  
  // 生成新组件实例
  const newComponent: ComponentInstance = {
    id: generateId(),
    type: componentType,
    props: getDefaultProps(componentType),
    position: dropPosition
  };
  
  // 添加到 Schema
  dispatch({
    type: 'ADD_COMPONENT',
    payload: newComponent
  });
  
  // 选中新组件
  dispatch({
    type: 'SELECT_COMPONENT',
    payload: newComponent.id
  });
};
```

### 3.3 属性更新逻辑
```typescript
const updateComponentProps = (componentId: string, props: any) => {
  dispatch({
    type: 'UPDATE_COMPONENT_PROPS',
    payload: { componentId, props }
  });
  
  // 实时更新预览
  updatePreview();
};
```

## 4. 关键功能实现

### 4.1 字段配置器
```typescript
const FieldConfigurator: React.FC<{
  fields: FieldConfig[];
  onChange: (fields: FieldConfig[]) => void;
}> = ({ fields, onChange }) => {
  const [editingField, setEditingField] = useState<FieldConfig | null>(null);
  
  const addField = () => {
    const newField: FieldConfig = {
      name: `field_${Date.now()}`,
      label: '新字段',
      type: 'input',
      required: false
    };
    
    onChange([...fields, newField]);
    setEditingField(newField);
  };
  
  const updateField = (index: number, field: FieldConfig) => {
    const newFields = [...fields];
    newFields[index] = field;
    onChange(newFields);
  };
  
  const deleteField = (index: number) => {
    const newFields = fields.filter((_, i) => i !== index);
    onChange(newFields);
  };
  
  return (
    <div className="field-configurator">
      <div className="field-list">
        {fields.map((field, index) => (
          <FieldItem
            key={field.name}
            field={field}
            onEdit={() => setEditingField(field)}
            onDelete={() => deleteField(index)}
          />
        ))}
      </div>
      
      <Button onClick={addField} type="dashed" block>
        添加字段
      </Button>
      
      {editingField && (
        <FieldEditor
          field={editingField}
          onSave={(field) => {
            const index = fields.findIndex(f => f.name === editingField.name);
            updateField(index, field);
            setEditingField(null);
          }}
          onCancel={() => setEditingField(null)}
        />
      )}
    </div>
  );
};
```

### 4.2 API 测试器
```typescript
const ApiTester: React.FC<{
  api: ApiConfig;
  onTest: (result: any) => void;
}> = ({ api, onTest }) => {
  const [testParams, setTestParams] = useState<Record<string, any>>({});
  const [testing, setTesting] = useState(false);
  const [result, setResult] = useState<any>(null);
  
  const handleTest = async () => {
    setTesting(true);
    try {
      const response = await fetch(api.url, {
        method: api.method,
        headers: {
          'Content-Type': 'application/json',
          ...api.headers
        },
        body: api.method !== 'GET' ? JSON.stringify(testParams) : undefined
      });
      
      const data = await response.json();
      setResult(data);
      onTest(data);
    } catch (error) {
      setResult({ error: error.message });
    } finally {
      setTesting(false);
    }
  };
  
  return (
    <div className="api-tester">
      <div className="test-params">
        <h4>测试参数</h4>
        {api.params.map(param => (
          <Input
            key={param}
            placeholder={param}
            value={testParams[param] || ''}
            onChange={(e) => setTestParams({
              ...testParams,
              [param]: e.target.value
            })}
          />
        ))}
      </div>
      
      <Button 
        onClick={handleTest} 
        loading={testing}
        type="primary"
      >
        测试接口
      </Button>
      
      {result && (
        <div className="test-result">
          <h4>返回结果</h4>
          <pre>{JSON.stringify(result, null, 2)}</pre>
        </div>
      )}
    </div>
  );
};
```

## 5. 导出功能

### 5.1 Schema 导出
```typescript
const exportSchema = () => {
  const schema = {
    version: '1.0.0',
    meta: {
      title: designerState.title,
      description: designerState.description
    },
    theme: designerState.theme,
    layout: 'single-column',
    components: designerState.components,
    apis: designerState.apis
  };
  
  // 下载 JSON 文件
  const blob = new Blob([JSON.stringify(schema, null, 2)], {
    type: 'application/json'
  });
  
  const url = URL.createObjectURL(blob);
  const a = document.createElement('a');
  a.href = url;
  a.download = `${schema.meta.title}.json`;
  a.click();
  
  URL.revokeObjectURL(url);
};
```

### 5.2 预览功能
```typescript
const PreviewModal: React.FC<{
  schema: Schema;
  visible: boolean;
  onClose: () => void;
}> = ({ schema, visible, onClose }) => {
  return (
    <Modal
      title="预览效果"
      visible={visible}
      onCancel={onClose}
      width="90%"
      footer={null}
    >
      <div className="preview-container">
        <Renderer schema={schema} />
      </div>
    </Modal>
  );
};
```
