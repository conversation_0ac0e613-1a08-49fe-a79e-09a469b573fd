import React from 'react';
import { ComponentMeta } from '../types';
export declare class ComponentRegistry {
    private components;
    private metas;
    register(meta: ComponentMeta, component?: React.ComponentType<any>): void;
    getComponent(type: string): React.ComponentType<any> | undefined;
    getMeta(type: string): ComponentMeta | undefined;
    getComponentTypes(): string[];
    getAllMetas(): ComponentMeta[];
    getComponentsByCategory(category: string): ComponentMeta[];
    hasComponent(type: string): boolean;
    unregister(type: string): void;
    clear(): void;
    size(): number;
}
//# sourceMappingURL=ComponentRegistry.d.ts.map