export declare class EventBus {
    private events;
    on(event: string, handler: Function): void;
    off(event: string, handler: Function): void;
    once(event: string, handler: Function): void;
    listenerCount(event: string): number;
    eventNames(): string[];
    removeAllListeners(event?: string): void;
    listeners(event: string): Function[];
    onWildcard(pattern: string, handler: Function): void;
    offWildcard(pattern: string, handler: Function): void;
    emit(event: string, ...args: any[]): void;
    namespace(ns: string): {
        on: (event: string, handler: Function) => void;
        off: (event: string, handler: Function) => void;
        emit: (event: string, ...args: any[]) => void;
        once: (event: string, handler: Function) => void;
    };
    getStats(): Record<string, number>;
    enableDebug(): void;
}
//# sourceMappingURL=EventBus.d.ts.map