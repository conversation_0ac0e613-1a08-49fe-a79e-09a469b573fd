// 重新导出shared包类型
export type {
  ComponentSchema,
  PageSchema,
  ApiConfig,
  ThemeConfig,
  LayoutConfig,
  ComponentMeta,
  PropMeta,
  EventMeta,
  RendererConfig,
} from '@lowcode/shared';

// 从shared包中引入类型以在本文件中引用
import type { ComponentMeta, PageSchema, ComponentSchema } from '@lowcode/shared';

// 设计器特有类型
export interface DesignerConfig {
  theme?: 'light' | 'dark';
  language?: 'zh' | 'en';
  plugins?: PluginConfig[];
}

// 插件配置
export interface PluginConfig {
  name: string;
  version: string;
  entry: string;
  config?: Record<string, any>;
}

// 拖拽项目
export interface DragItem {
  type: string;
  componentType: string;
  meta: ComponentMeta;
}

// 画布状态
export interface CanvasState {
  selectedComponentId?: string;
  hoveredComponentId?: string;
  scale: number;
  offset: { x: number; y: number };
  mode: 'design' | 'preview';
  componentPanelCollapsed: boolean;
}

// 历史记录
export interface HistoryState {
  schema: PageSchema;
  timestamp: number;
  description: string;
}

// 设计器上下文
export interface DesignerContext {
  schema: PageSchema;
  canvasState: CanvasState;
  history: HistoryState[];
  currentHistoryIndex: number;
  updateSchema: (schema: PageSchema) => void;
  selectComponent: (id?: string) => void;
  hoverComponent: (id?: string) => void;
  addComponent: (component: ComponentSchema, parentId?: string, index?: number) => void;
  removeComponent: (id: string) => void;
  updateComponent: (id: string, updates: Partial<ComponentSchema>) => void;
  undo: () => void;
  redo: () => void;
  setCanvasMode: (mode: 'design' | 'preview') => void;
  setCanvasScale: (scale: number) => void;
  setCanvasOffset: (offset: { x: number; y: number }) => void;
  toggleComponentPanel: () => void;
}

// 属性编辑器配置
export interface PropertyEditorConfig {
  component: ComponentSchema;
  meta: ComponentMeta;
  onChange: (updates: Partial<ComponentSchema>) => void;
}

// 组件面板项目
export interface ComponentPanelItem {
  category: string;
  components: ComponentMeta[];
}

// 工具栏动作
export interface ToolbarAction {
  key: string;
  label: string;
  icon?: string;
  disabled?: boolean;
  onClick: () => void;
}
