import React from 'react';
export interface TopDownLayoutProps {
    header?: React.ReactNode;
    sidebar?: React.ReactNode;
    footer?: React.ReactNode;
    children?: React.ReactNode;
    sidebarWidth?: number;
    headerHeight?: number;
    footerHeight?: number;
    sidebarCollapsed?: boolean;
    sidebarCollapsible?: boolean;
    style?: React.CSSProperties;
    className?: string;
    onSidebarToggle?: (collapsed: boolean) => void;
}
export declare const TopDownLayout: React.FC<TopDownLayoutProps>;
//# sourceMappingURL=TopDownLayout.d.ts.map
