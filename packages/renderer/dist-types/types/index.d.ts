export interface ComponentSchema {
    id: string;
    type: string;
    props?: Record<string, any>;
    children?: ComponentSchema[];
    events?: Record<string, string>;
    style?: Record<string, any>;
    className?: string;
}
export interface PageSchema {
    id: string;
    title: string;
    components: ComponentSchema[];
    apis?: ApiConfig[];
    theme?: ThemeConfig;
    layout?: LayoutConfig;
}
export interface ApiConfig {
    id: string;
    name: string;
    url: string;
    method: 'GET' | 'POST' | 'PUT' | 'DELETE';
    headers?: Record<string, string>;
    params?: Record<string, any>;
    dataPath?: string;
}
export interface ThemeConfig {
    primaryColor?: string;
    backgroundColor?: string;
    textColor?: string;
    borderColor?: string;
    borderRadius?: number;
    fontSize?: number;
}
export interface LayoutConfig {
    type: 'admin' | 'topdown' | 'simple' | 'custom';
    header?: boolean;
    sidebar?: boolean;
    footer?: boolean;
}
export interface ComponentMeta {
    type: string;
    name: string;
    description?: string;
    category: string;
    icon?: string;
    props: PropMeta[];
    events?: EventMeta[];
    defaultProps?: Record<string, any>;
    preview?: string;
}
export interface PropMeta {
    name: string;
    type: 'string' | 'number' | 'boolean' | 'object' | 'array' | 'function';
    required?: boolean;
    default?: any;
    description?: string;
    options?: Array<{
        label: string;
        value: any;
    }>;
}
export interface EventMeta {
    name: string;
    description?: string;
    params?: Array<{
        name: string;
        type: string;
        description?: string;
    }>;
}
export interface RendererConfig {
    theme?: ThemeConfig;
    apis?: Record<string, ApiConfig>;
    components?: Record<string, ComponentMeta>;
}
export interface EventBus {
    on(event: string, handler: Function): void;
    off(event: string, handler: Function): void;
    emit(event: string, ...args: any[]): void;
}
export interface ComponentInstance {
    id: string;
    type: string;
    props: Record<string, any>;
    ref?: any;
    children?: ComponentInstance[];
}
//# sourceMappingURL=index.d.ts.map