# 低代码平台模拟数据与事件配置设计方案

## 1. 系统架构理解

### 1.1 核心概念
- **低代码开发平台**：可视化设计工具，用于创建和配置页面，产出JSON Schema
- **低代码运行平台**：生产环境渲染引擎，消费JSON Schema并渲染页面
- **共享基础设施**：组件库、主题系统、API管理等，两个平台共享使用

### 1.2 数据流转机制
```
设计器配置 → JSON Schema → 运行时渲染
     ↓           ↓            ↓
  事件配置    模拟数据      API请求
  API配置     数据映射      数据绑定
```

## 2. 现有实现分析

### 2.1 组件数据获取机制
以SidebarTreeView为例，组件通过以下方式获取数据：

1. **模拟数据获取**：
   ```typescript
   const mockData = useMockData(componentId);
   const treeData: TreeNode[] = React.useMemo(() => {
     if (mockData && mockData.length > 0) {
       return mockData;
     }
     return []; // 没有模拟数据时返回空数组
   }, [mockData]);
   ```

2. **事件触发机制**：
   ```typescript
   // 组件挂载时触发初始加载事件
   React.useEffect(() => {
     eventHandler.onMount({
       componentId,
       timestamp: Date.now()
     });
   }, [componentId, eventHandler]);
   ```

### 2.2 事件系统架构
- **useEventHandler Hook**：处理组件事件，支持API调用
- **预定义事件配置**：每种组件类型都有预定义的事件（onMount、onNodeClick等）
- **API配置**：每个事件可以配置对应的API请求

### 2.3 API响应数据格式
标准API响应格式：
```json
{
  "status": "success",
  "data": [
    {"id": 1, "name": "test"},
    {"id": 2, "name": "test2"}
  ],
  "message": "操作成功"
}
```
组件需要映射的是`data`字段中的内容。

## 3. 设计方案

### 3.1 API配置与模拟数据集成流程

#### 3.1.1 设计器端配置
1. **选择组件**：在设计器中选择需要配置数据的组件
2. **打开事件面板**：通过EventPanel配置事件和API
3. **配置API请求**：
   - URL：API请求地址
   - Method：请求方法
   - Headers：请求头
   - Params：请求参数
4. **配置模拟数据**：在API配置下方直接配置模拟返回数据
   - 模拟数据格式与API响应的data字段格式一致
   - 提供JSON编辑器进行数据编辑
5. **保存配置**：将API配置和模拟数据保存到组件的events字段

#### 3.1.2 运行时数据获取
1. **开发模式**：组件直接使用配置的模拟数据
2. **生产模式**：组件调用真实API，从响应的data字段提取数据
3. **错误处理**：API请求失败时显示系统错误提示，不降级到模拟数据

### 3.2 事件配置流程

#### 3.2.1 事件类型定义
每个组件类型都有预定义的事件：

**SidebarTreeView事件**：
- `onMount`：组件挂载时触发，用于加载树形数据
- `onNodeClick`：节点点击时触发
- `onNodeExpand`：节点展开时触发
- `onSearch`：搜索时触发

**TableViewWithSearch事件**：
- `onMount`：组件挂载时触发，用于加载表格数据
- `onSearch`：搜索时触发
- `onRowClick`：行点击时触发
- `onPageChange`：分页变化时触发
- `onToolbarAction`：工具栏操作时触发

#### 3.2.2 事件配置步骤
1. **选择组件**：在设计器中选择组件
2. **打开事件面板**：通过EventPanel配置事件
3. **配置API**：为每个事件配置对应的API请求
   - URL：API请求地址
   - Method：请求方法（GET/POST/PUT/DELETE）
   - Headers：请求头
   - Params：请求参数
   - Body：请求体
   - Timeout：超时时间
4. **配置模拟数据**：在API配置下方配置模拟返回数据
   - 数据格式与API响应的data字段一致
   - 用于开发时预览和测试
5. **保存配置**：将事件配置保存到组件的events字段

### 3.3 数据映射关系

#### 3.3.1 API响应数据映射
简化的API配置结构：

```typescript
interface ApiConfig {
  url: string;
  method: 'GET' | 'POST' | 'PUT' | 'DELETE';
  headers?: Record<string, string>;
  params?: Record<string, any>;
  body?: any;
  timeout?: number;
  mockData?: any; // 模拟数据，格式与API响应的data字段一致
}
```

#### 3.3.2 数据处理逻辑
1. **开发模式**：直接使用mockData
2. **生产模式**：
   - 调用API获取响应：`{status, data, message}`
   - 提取data字段作为组件数据
   - 请求失败时显示错误提示，不使用模拟数据

## 4. 实现细节

### 4.1 组件改造要点

#### 4.1.1 SidebarTreeView组件
- ✅ 已实现useMockData获取模拟数据
- ✅ 已实现事件触发机制（onMount、onNodeClick等）
- ✅ 已支持ComponentDataContext数据上下文

#### 4.1.2 需要完善的功能
1. **API数据获取**：在事件触发时调用真实API
2. **数据提取**：从API响应的data字段提取组件数据
3. **错误处理**：API请求失败时显示系统错误

### 4.2 设计器配置界面优化

#### 4.2.1 移除独立的MockDataPanel
- 🔄 将模拟数据配置集成到EventPanel中
- 🔄 在API配置表单下方添加模拟数据编辑区域

#### 4.2.2 EventPanel（事件配置面板）增强
- ✅ 已实现事件列表展示
- ✅ 已实现API配置表单
- 🔄 需要添加模拟数据配置区域
- 🔄 简化数据格式，移除多接口支持

### 4.3 运行时数据处理简化

#### 4.3.1 数据获取逻辑
1. **开发模式**：使用事件配置中的mockData
2. **生产模式**：调用API，提取response.data字段
3. **错误处理**：显示系统错误，不降级

#### 4.3.2 移除复杂功能
- 🚫 移除API响应缓存（保持简单）
- 🚫 移除多格式兼容（只使用新格式）
- 🚫 移除降级机制（避免误导用户）

## 5. 配置示例

### 5.1 SidebarTreeView配置示例

#### 5.1.1 简化的事件配置
```json
{
  "onMount": {
    "type": "callApi",
    "config": {
      "id": "onMount",
      "apiConfig": {
        "url": "/api/tree/data",
        "method": "GET",
        "headers": {"Content-Type": "application/json"},
        "timeout": 5000,
        "mockData": [
          {
            "key": "all",
            "title": "全部API",
            "count": 212
          },
          {
            "key": "penetration",
            "title": "渗透测试重点API",
            "children": [
              {
                "key": "login",
                "title": "登录API",
                "count": 26
              }
            ]
          }
        ]
      }
    }
  }
}
```

#### 5.1.2 API响应格式
```json
{
  "status": "success",
  "data": [
    {
      "key": "all",
      "title": "全部API",
      "count": 212
    },
    {
      "key": "penetration",
      "title": "渗透测试重点API",
      "children": [
        {
          "key": "login",
          "title": "登录API",
          "count": 26
        }
      ]
    }
  ],
  "message": "获取成功"
}
```

### 5.2 完整Schema示例
参考现有的`examples/schemas/api-management.json`，展示了完整的配置结构。

## 6. 开发建议

### 6.1 优先级排序
1. **高优先级**：将模拟数据配置集成到EventPanel中
2. **中优先级**：完善API数据获取和data字段提取
3. **低优先级**：添加更多组件类型的事件支持

### 6.2 技术实现原则
1. **简单高效**：移除历史包袱，使用最简单的实现方式
2. **单一格式**：只支持新的简化格式，不兼容旧格式
3. **清晰分离**：开发模式用模拟数据，生产模式用API数据

### 6.3 用户体验
1. **集成配置**：在同一个面板中配置API和模拟数据
2. **实时预览**：开发模式下使用模拟数据立即预览效果
3. **明确错误**：生产模式API失败时显示明确的系统错误

## 7. 总结

优化后的设计方案更加简洁高效：

1. **移除MockDataPanel**：将模拟数据配置集成到EventPanel中
2. **简化数据格式**：只使用一种格式，mockData直接存储在apiConfig中
3. **明确数据流**：开发用模拟数据，生产用API的data字段，失败显示错误
4. **移除降级机制**：避免在生产环境中误导用户

这样的设计既保持了功能完整性，又大大简化了系统复杂度，更适合研究阶段的快速迭代。
