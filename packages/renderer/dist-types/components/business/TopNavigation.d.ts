import React from 'react';
export interface TopNavigationProps {
    logo?: {
        src?: string;
        alt?: string;
        text?: string;
        href?: string;
    };
    menu?: Array<{
        key: string;
        label: string;
        href?: string;
        children?: Array<{
            key: string;
            label: string;
            href?: string;
        }>;
    }>;
    user?: {
        name?: string;
        avatar?: string;
        menu?: Array<{
            key: string;
            label: string;
            onClick?: () => void;
        }>;
    };
    actions?: Array<{
        key: string;
        label: string;
        icon?: string;
        onClick?: () => void;
    }>;
    style?: React.CSSProperties;
    className?: string;
    onMenuClick?: (key: string) => void;
    onUserMenuClick?: (key: string) => void;
}
export declare const TopNavigation: React.FC<TopNavigationProps>;
//# sourceMappingURL=TopNavigation.d.ts.map