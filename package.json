{"name": "lowcode-platform", "version": "1.0.0", "description": "Low-code platform MVP with renderer and designer", "private": true, "workspaces": ["packages/*"], "scripts": {"dev": "npm run dev --workspaces", "build": "npm run build --workspaces", "test": "npm run test --workspaces", "lint": "npm run lint --workspaces", "clean": "npm run clean --workspaces"}, "devDependencies": {"@types/node": "^20.0.0", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "eslint": "^8.45.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.3", "prettier": "^3.0.0", "typescript": "^5.0.0"}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}}