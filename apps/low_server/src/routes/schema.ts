import express from 'express';
import path from 'path';
import fs from 'fs-extra';

const router = express.Router();

// Schema 文件存储目录
const SCHEMAS_DIR = path.resolve(__dirname, '../../../../examples/schemas');

// 确保 schemas 目录存在
fs.ensureDirSync(SCHEMAS_DIR);

// 获取所有 Schema 文件列表
router.get('/', async (req, res) => {
  try {
    const files = await fs.readdir(SCHEMAS_DIR);
    const schemaFiles = files.filter(file => file.endsWith('.json'));
    
    const schemas = await Promise.all(
      schemaFiles.map(async (filename) => {
        const filePath = path.join(SCHEMAS_DIR, filename);
        const stats = await fs.stat(filePath);
        
        try {
          const content = await fs.readJson(filePath);
          return {
            filename,
            id: content.id || filename.replace('.json', ''),
            title: content.title || filename.replace('.json', ''),
            description: content.description || '',
            size: stats.size,
            lastModified: stats.mtime,
            createdAt: stats.birthtime
          };
        } catch (error) {
          return {
            filename,
            id: filename.replace('.json', ''),
            title: filename.replace('.json', ''),
            description: '无法解析的文件',
            size: stats.size,
            lastModified: stats.mtime,
            createdAt: stats.birthtime,
            error: 'Invalid JSON format'
          };
        }
      })
    );

    res.json({
      success: true,
      data: schemas,
      total: schemas.length
    });
  } catch (error) {
    console.error('Error reading schemas directory:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to read schemas directory',
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// 获取单个 Schema 文件
router.get('/:filename', async (req, res) => {
  try {
    const { filename } = req.params;
    
    // 安全检查：确保文件名只包含安全字符
    if (!/^[a-zA-Z0-9_-]+\.json$/.test(filename)) {
      return res.status(400).json({
        success: false,
        error: 'Invalid filename format'
      });
    }

    const filePath = path.join(SCHEMAS_DIR, filename);
    
    // 检查文件是否存在
    if (!await fs.pathExists(filePath)) {
      return res.status(404).json({
        success: false,
        error: 'Schema file not found'
      });
    }

    const schema = await fs.readJson(filePath);
    const stats = await fs.stat(filePath);

    res.json({
      success: true,
      data: {
        ...schema,
        _metadata: {
          filename,
          size: stats.size,
          lastModified: stats.mtime,
          createdAt: stats.birthtime
        }
      }
    });
  } catch (error) {
    console.error('Error reading schema file:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to read schema file',
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// 保存 Schema 文件
router.post('/', async (req, res) => {
  try {
    const schema = req.body;
    
    // 验证 Schema 基本结构
    if (!schema.id || !schema.components) {
      return res.status(400).json({
        success: false,
        error: 'Invalid schema format: missing required fields (id, components)'
      });
    }

    // 生成文件名
    const filename = `${schema.id}.json`;
    const filePath = path.join(SCHEMAS_DIR, filename);

    // 添加元数据
    const schemaWithMetadata = {
      ...schema,
      _metadata: {
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        version: '1.0.0'
      }
    };

    // 保存文件
    await fs.writeJson(filePath, schemaWithMetadata, { spaces: 2 });

    res.json({
      success: true,
      message: 'Schema saved successfully',
      data: {
        filename,
        id: schema.id,
        title: schema.title,
        path: `/examples/schemas/${filename}`
      }
    });
  } catch (error) {
    console.error('Error saving schema:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to save schema',
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// 更新 Schema 文件
router.put('/:filename', async (req, res) => {
  try {
    const { filename } = req.params;
    const schema = req.body;
    
    // 安全检查
    if (!/^[a-zA-Z0-9_-]+\.json$/.test(filename)) {
      return res.status(400).json({
        success: false,
        error: 'Invalid filename format'
      });
    }

    // 验证 Schema 基本结构
    if (!schema.id || !schema.components) {
      return res.status(400).json({
        success: false,
        error: 'Invalid schema format: missing required fields (id, components)'
      });
    }

    const filePath = path.join(SCHEMAS_DIR, filename);
    
    // 检查文件是否存在
    if (!await fs.pathExists(filePath)) {
      return res.status(404).json({
        success: false,
        error: 'Schema file not found'
      });
    }

    // 读取现有文件获取创建时间
    let createdAt = new Date().toISOString();
    try {
      const existingSchema = await fs.readJson(filePath);
      createdAt = existingSchema._metadata?.createdAt || createdAt;
    } catch (error) {
      // 如果读取失败，使用当前时间
    }

    // 添加/更新元数据
    const schemaWithMetadata = {
      ...schema,
      _metadata: {
        createdAt,
        updatedAt: new Date().toISOString(),
        version: '1.0.0'
      }
    };

    // 保存文件
    await fs.writeJson(filePath, schemaWithMetadata, { spaces: 2 });

    res.json({
      success: true,
      message: 'Schema updated successfully',
      data: {
        filename,
        id: schema.id,
        title: schema.title,
        path: `/examples/schemas/${filename}`
      }
    });
  } catch (error) {
    console.error('Error updating schema:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to update schema',
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// 删除 Schema 文件
router.delete('/:filename', async (req, res) => {
  try {
    const { filename } = req.params;
    
    // 安全检查
    if (!/^[a-zA-Z0-9_-]+\.json$/.test(filename)) {
      return res.status(400).json({
        success: false,
        error: 'Invalid filename format'
      });
    }

    const filePath = path.join(SCHEMAS_DIR, filename);
    
    // 检查文件是否存在
    if (!await fs.pathExists(filePath)) {
      return res.status(404).json({
        success: false,
        error: 'Schema file not found'
      });
    }

    // 删除文件
    await fs.remove(filePath);

    res.json({
      success: true,
      message: 'Schema deleted successfully',
      data: {
        filename
      }
    });
  } catch (error) {
    console.error('Error deleting schema:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to delete schema',
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

export { router as schemaRoutes };
