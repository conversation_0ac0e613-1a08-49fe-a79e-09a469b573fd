name: CI

on:
  push:
    branches: [ main ]
  pull_request:
    branches: [ main ]

jobs:
  build:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-node@v4
        with:
          node-version: 18
      - run: npm ci
      - run: npm -w @lowcode/renderer run build
      - run: npx tsc -p packages/renderer/tsconfig.json --emitDeclarationOnly --declaration --outDir packages/renderer/dist-types
      - run: npm -w @lowcode/designer run build

