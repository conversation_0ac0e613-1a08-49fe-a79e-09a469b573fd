// 事件动作配置
export interface EventAction {
  type: 'navigate' | 'showMessage' | 'updateState' | 'callApi' | 'customCode';
  config: Record<string, any>;
}

// 组件Schema定义
export interface ComponentSchema {
  id: string;
  type: string;
  props?: Record<string, any>;
  children?: ComponentSchema[];
  events?: Record<string, EventAction>;
  style?: Record<string, any>;
  className?: string;
  mockData?: string; // JSON字符串格式的模拟数据
}

// 页面Schema定义
export interface PageSchema {
  id: string;
  title: string;
  components: ComponentSchema[];
  apis?: ApiConfig[];
  theme?: ThemeConfig;
  layout?: LayoutConfig;
}

// API配置
export interface ApiConfig {
  id: string;
  name: string;
  url: string;
  method: 'GET' | 'POST' | 'PUT' | 'DELETE';
  headers?: Record<string, string>;
  params?: Record<string, any>;
  body?: any;
  timeout?: number;
  mockData?: any; // 模拟数据，格式与API响应的data字段一致
}

// 主题配置
export interface ThemeConfig {
  primaryColor?: string;
  backgroundColor?: string;
  textColor?: string;
  borderColor?: string;
  borderRadius?: number;
  fontSize?: number;
}

// 布局配置
export interface LayoutConfig {
  type: 'admin' | 'topdown' | 'simple' | 'custom';
  header?: boolean;
  sidebar?: boolean;
  footer?: boolean;
}

// 组件注册信息
export interface ComponentMeta {
  type: string;
  name: string;
  description?: string;
  category: string;
  icon?: string;
  props: PropMeta[];
  events?: EventMeta[];
  defaultProps?: Record<string, any>;
  preview?: string;
}

// 属性元数据
export interface PropMeta {
  name: string;
  type: 'string' | 'number' | 'boolean' | 'object' | 'array' | 'function';
  required?: boolean;
  default?: any;
  description?: string;
  options?: Array<{ label: string; value: any }>;
}

// 事件元数据
export interface EventMeta {
  name: string;
  description?: string;
  params?: Array<{ name: string; type: string; description?: string }>;
}

// 渲染器配置
export interface RendererConfig {
  theme?: ThemeConfig;
  apis?: Record<string, ApiConfig>;
  components?: Record<string, ComponentMeta>;
}

// 事件系统
export interface EventBus {
  on(event: string, handler: Function): void;
  off(event: string, handler: Function): void;
  emit(event: string, ...args: any[]): void;
}

// 组件实例
export interface ComponentInstance {
  id: string;
  type: string;
  props: Record<string, any>;
  ref?: any;
  children?: ComponentInstance[];
}
