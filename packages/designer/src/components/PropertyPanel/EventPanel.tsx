import React, { useState } from 'react';
import {
  Input,
  Select,
  Space,
  Typography,
  Divider,
  Alert,
  Badge
} from 'antd';
import {
  ApiOutlined,
  SettingOutlined,
  InfoCircleOutlined,
  ThunderboltOutlined
} from '@ant-design/icons';
import Editor from '@monaco-editor/react';
import { useDesigner } from '../../context/DesignerContext';
import { findComponent } from '@lowcode/renderer';
import { getComponentEvents, EventMeta, ApiConfig } from '../../types/events';
import { SimpleDataMappingConfig, type DataMapping } from './SimpleDataMappingConfig';
import { SidebarTreeViewMappableFields, TableViewWithSearchMappableFields } from '@lowcode/shared';

const { Title, Text } = Typography;

export interface EventPanelProps {
  width?: number;
}

export const EventPanel: React.FC<EventPanelProps> = ({ width: _width = 576 }) => {
  const { schema, canvasState, updateComponent } = useDesigner();
  const [selectedEventId, setSelectedEventId] = useState<string | null>(null);

  // 将所有状态提升到组件顶层
  const [headersText, setHeadersText] = useState('{}');
  const [mockDataText, setMockDataText] = useState('[]');

  // 当前配置状态
  const [currentConfig, setCurrentConfig] = useState<ApiConfig | null>(null);

  // 获取当前选中的组件
  const selectedComponent = canvasState.selectedComponentId
    ? findComponent(schema.components, canvasState.selectedComponentId)
    : null;

  // 获取组件类型的预定义事件
  const predefinedEvents = selectedComponent ? getComponentEvents(selectedComponent.type) : [];

  // 获取组件的事件配置
  const componentEvents = selectedComponent?.events || {};

  // 自动选择第一个事件（如果没有选中任何事件）
  React.useEffect(() => {
    if (!selectedEventId && predefinedEvents.length > 0) {
      setSelectedEventId(predefinedEvents[0].id);
    }
  }, [selectedEventId, predefinedEvents]);

  // 同步选中事件的配置状态
  React.useEffect(() => {
    if (selectedEventId && componentEvents[selectedEventId]) {
      const config = componentEvents[selectedEventId].config?.apiConfig;
      if (config) {
        setCurrentConfig(config);
        setHeadersText(JSON.stringify(config.headers || {}, null, 2));
        setMockDataText(JSON.stringify(config.mockData || [], null, 2));
      }
    } else if (selectedEventId && selectedComponent) {
      // 如果选中了事件但没有配置，使用默认配置
      const eventMeta = predefinedEvents.find(e => e.id === selectedEventId);
      if (eventMeta) {
        const defaultConfig = {
          url: eventMeta.defaultApiConfig?.url || '',
          method: eventMeta.defaultApiConfig?.method || 'GET',
          headers: eventMeta.defaultApiConfig?.headers || {},
          params: {},
          body: undefined,
          timeout: 5000,
          mockData: getDefaultMockData(selectedComponent.type)
        };
        setCurrentConfig(defaultConfig);
        setHeadersText(JSON.stringify(defaultConfig.headers || {}, null, 2));
        setMockDataText(JSON.stringify(defaultConfig.mockData || [], null, 2));
      }
    } else {
      setCurrentConfig(null);
    }
  }, [selectedEventId, componentEvents, predefinedEvents, selectedComponent]);

  // 获取组件的可映射字段
  const getComponentMappableFields = (componentType: string) => {
    switch (componentType) {
      case 'SidebarTreeView':
        return SidebarTreeViewMappableFields;
      case 'TableViewWithSearch':
        return TableViewWithSearchMappableFields;
      default:
        return {};
    }
  };

  if (!selectedComponent) {
    return (
      <div style={{ padding: '24px' }}>
        <Alert
          message="请先选择一个组件"
          description="选择画布中的组件来配置其事件"
          type="info"
          showIcon
          icon={<InfoCircleOutlined />}
        />
      </div>
    );
  }

  // 如果组件类型不支持事件配置
  if (predefinedEvents.length === 0) {
    return (
      <div style={{ padding: '24px' }}>
        <Alert
          message="该组件类型暂不支持事件配置"
          description={
            <div>
              <div>当前组件：{selectedComponent.type}</div>
              <div style={{ marginTop: '8px' }}>
                支持的组件：TableViewWithSearch、SidebarTreeView、TopNavigation
              </div>
            </div>
          }
          type="warning"
          showIcon
        />
      </div>
    );
  }

  // 更新事件配置
  const updateEventConfig = (eventId: string, apiConfig: ApiConfig) => {
    const newEvents = {
      ...componentEvents,
      [eventId]: {
        type: 'callApi' as const,
        config: {
          id: eventId,
          apiConfig
        }
      }
    };
    updateComponent(selectedComponent.id, { events: newEvents });
  };



  // 获取默认模拟数据
  const getDefaultMockData = (componentType: string) => {
    if (componentType === 'SidebarTreeView') {
      return [
        { key: 'all', title: '全部API', count: 212 },
        {
          key: 'penetration',
          title: '渗透测试重点API',
          children: [
            { key: 'login', title: '登录API', count: 26 },
            { key: 'url', title: 'URL重定向API', count: 5 }
          ]
        }
      ];
    } else if (componentType === 'TableViewWithSearch') {
      return [
        { id: 1, path: '/login', sensitivity: '高敏感', riskLevel: '高风险', totalVisits: '3.6千', trafficSource: '192.168.0.1', firstSeen: '2025-08-14 19:18:21' },
        { id: 2, path: '/abnormal', sensitivity: '高敏感', riskLevel: '高风险', totalVisits: '2.5千', trafficSource: '192.168.0.1', firstSeen: '2025-08-14 19:19:12' }
      ];
    }
    return [];
  };

  // 渲染API配置表单
  const renderApiConfigForm = (eventMeta: EventMeta) => {
    if (!currentConfig) return null;

    const handleConfigChange = (field: keyof ApiConfig, value: any) => {
      const newConfig = { ...currentConfig, [field]: value };
      setCurrentConfig(newConfig);
      updateEventConfig(eventMeta.id, newConfig);
    };

    return (
      <div style={{ padding: '12px', backgroundColor: '#fafafa' }}>
        <Space direction="vertical" size="small" style={{ width: '100%' }}>
          {/* API基础配置 */}
          <div>
            <Title level={5} style={{ margin: '0 0 8px 0', fontSize: '13px' }}>
              <ApiOutlined style={{ marginRight: '4px', color: '#1890ff' }} />
              API配置
            </Title>

            <Space direction="vertical" size={4} style={{ width: '100%' }}>
              <div>
                <Text strong style={{ fontSize: '11px' }}>接口地址</Text>
                <Input
                  value={currentConfig.url}
                  onChange={(e) => handleConfigChange('url', e.target.value)}
                  placeholder="请输入接口地址，如：/api/data"
                  prefix={<ApiOutlined style={{ color: '#bfbfbf' }} />}
                  style={{ marginTop: '2px' }}
                  size="small"
                />
              </div>

              <div>
                <Text strong style={{ fontSize: '11px' }}>请求方法</Text>
                <Select
                  value={currentConfig.method}
                  onChange={(value) => handleConfigChange('method', value)}
                  style={{ width: '100%', marginTop: '2px' }}
                  size="small"
                  options={[
                    { label: 'GET', value: 'GET' },
                    { label: 'POST', value: 'POST' },
                    { label: 'PUT', value: 'PUT' },
                    { label: 'DELETE', value: 'DELETE' }
                  ]}
                />
              </div>
            </Space>
          </div>

          <Divider style={{ margin: '8px 0' }} />

          {/* 请求头配置 */}
          <div>
            <Title level={5} style={{ margin: '0 0 8px 0', fontSize: '13px' }}>
              <SettingOutlined style={{ marginRight: '4px', color: '#52c41a' }} />
              请求头配置
            </Title>
            <div style={{ border: '1px solid #d9d9d9', borderRadius: '4px', overflow: 'hidden' }}>
              <Editor
                height="60px"
                language="json"
                value={headersText}
                onChange={(value) => {
                  const newValue = value || '{}';
                  setHeadersText(newValue);
                  try {
                    const headers = JSON.parse(newValue);
                    handleConfigChange('headers', headers);
                  } catch (error) {
                    // 忽略JSON解析错误
                  }
                }}
                options={{
                  minimap: { enabled: false },
                  scrollBeyondLastLine: false,
                  fontSize: 11,
                  lineNumbers: 'off',
                  folding: false,
                  wordWrap: 'on',
                  automaticLayout: true
                }}
              />
            </div>
          </div>

          <Divider style={{ margin: '8px 0' }} />

          {/* 模拟数据配置 */}
          <div>
            <Title level={5} style={{ margin: '0 0 8px 0', fontSize: '13px' }}>
              <ThunderboltOutlined style={{ marginRight: '4px', color: '#fa8c16' }} />
              模拟数据配置
            </Title>
            <div style={{ border: '1px solid #d9d9d9', borderRadius: '4px', overflow: 'hidden' }}>
              <Editor
                height="400px"
                language="json"
                value={mockDataText}
                onChange={(value) => {
                  const newValue = value || '[]';
                  setMockDataText(newValue);
                  try {
                    const mockData = JSON.parse(newValue);
                    handleConfigChange('mockData', mockData);
                  } catch (error) {
                    // 忽略JSON解析错误
                  }
                }}
                options={{
                  minimap: { enabled: false },
                  scrollBeyondLastLine: false,
                  fontSize: 11,
                  lineNumbers: 'on',
                  folding: true,
                  wordWrap: 'on',
                  automaticLayout: true
                }}
              />
            </div>
          </div>

          <Divider style={{ margin: '8px 0' }} />

          {/* 数据映射配置 */}
          <div>
            <Title level={5} style={{ margin: '0 0 8px 0', fontSize: '13px' }}>
              <SettingOutlined style={{ marginRight: '4px', color: '#722ed1' }} />
              数据映射配置
            </Title>
            <div style={{
              border: '1px solid #e8e8e8',
              borderRadius: '4px',
              backgroundColor: '#ffffff',
              overflow: 'hidden'
            }}>
              <SimpleDataMappingConfig
                mappableFields={getComponentMappableFields(selectedComponent.type)}
                value={currentConfig.dataMapping || {}}
                onChange={(mapping: DataMapping) => {
                  handleConfigChange('dataMapping', mapping);
                }}
              />
            </div>
          </div>

          {/* 操作按钮 */}

        </Space>
      </div>
    );
  };

  return (
    <div style={{ height: '100%', backgroundColor: '#ffffff' }}>
      {/* 事件选择和配置 */}
      <div style={{ padding: '16px', height: '100%', overflow: 'auto' }}>
        <Space direction="vertical" size="middle" style={{ width: '100%' }}>
          {/* 事件选择器 */}
          <div>
            <Text strong style={{ fontSize: '13px', marginBottom: '8px', display: 'block' }}>
              选择事件类型
            </Text>
            <Select
              value={selectedEventId}
              onChange={(value) => setSelectedEventId(value)}
              placeholder="请选择要配置的事件"
              style={{ width: '100%' }}
              size="small"
              allowClear
              onClear={() => setSelectedEventId(null)}
            >
              {predefinedEvents.map((eventMeta) => {
                const isConfigured = componentEvents[eventMeta.id];
                return (
                  <Select.Option key={eventMeta.id} value={eventMeta.id}>
                    <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                      <div style={{ display: 'flex', alignItems: 'center', gap: '6px' }}>
                        <ThunderboltOutlined style={{ fontSize: '12px', color: '#1890ff' }} />
                        <span style={{ fontSize: '12px' }}>{eventMeta.name}</span>
                      </div>
                      {isConfigured && (
                        <Badge status="success" />
                      )}
                    </div>
                  </Select.Option>
                );
              })}
            </Select>
          </div>

          {/* 选中事件的描述 */}
          {selectedEventId && (
            <div style={{
              padding: '8px 12px',
              backgroundColor: '#f6f8fa',
              borderRadius: '4px',
              border: '1px solid #e1e4e8'
            }}>
              {(() => {
                const eventMeta = predefinedEvents.find(e => e.id === selectedEventId);
                return eventMeta ? (
                  <div>
                    <Text style={{ fontSize: '11px', color: '#586069' }}>
                      {eventMeta.description} · 触发时机：{eventMeta.trigger}
                    </Text>
                  </div>
                ) : null;
              })()}
            </div>
          )}

          {/* 事件配置表单 */}
          {selectedEventId && (
            <div style={{
              border: '1px solid #e8e8e8',
              borderRadius: '6px',
              backgroundColor: '#ffffff'
            }}>
              {(() => {
                const eventMeta = predefinedEvents.find(e => e.id === selectedEventId);
                return eventMeta ? renderApiConfigForm(eventMeta) : null;
              })()}
            </div>
          )}
        </Space>
      </div>
    </div>
  );
};