{"name": "@lowcode/designer", "version": "1.0.0", "description": "Low-code platform visual designer", "main": "src/index.ts", "module": "src/index.ts", "types": "src/index.ts", "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview", "test": "vitest", "lint": "eslint src --ext .ts,.tsx", "clean": "<PERSON><PERSON><PERSON> dist"}, "dependencies": {"@lowcode/renderer": "file:../renderer", "@lowcode/shared": "file:../shared", "@monaco-editor/react": "^4.7.0", "antd": "^5.0.0", "lodash": "^4.17.21", "monaco-editor": "^0.44.0", "react": ">=18.0.0", "react-dnd": "^16.0.0", "react-dnd-html5-backend": "^16.0.0", "react-dom": ">=18.0.0"}, "devDependencies": {"@types/lodash": "^4.14.0", "@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "@vitejs/plugin-react": "^4.0.0", "eslint": "^8.0.0", "rimraf": "^5.0.0", "typescript": "^5.0.0", "vite": "^4.0.0", "vitest": "^0.34.0"}, "peerDependencies": {"react": ">=18.0.0", "react-dom": ">=18.0.0"}}