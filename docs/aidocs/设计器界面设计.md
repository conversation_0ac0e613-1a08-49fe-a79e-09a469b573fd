# 设计器界面设计

## 1. 设计器整体布局

```
┌─────────────────────────────────────────────────────────────────────────────┐
│                              顶部工具栏                                      │
│  [新建] [保存] [预览] [导出] [导入]                    [主题选择] [帮助]      │
├─────────────────┬─────────────────────────────┬─────────────────────────────┤
│                 │                             │                             │
│   业务组件面板   │          画布区域            │        配置面板             │
│                 │                             │                             │
│ ┌─────────────┐ │  ┌─────────────────────────┐ │  ┌─────────────────────────┐ │
│ │ 布局组件    │ │  │                         │ │  │                         │ │
│ │ - 管理布局  │ │  │      页面预览区域        │ │  │      组件配置区域        │ │
│ │ - 简单布局  │ │  │                         │ │  │                         │ │
│ └─────────────┘ │  │  [选中组件高亮显示]     │ │  │  - 基础属性             │ │
│                 │  │                         │ │  │  - 数据配置             │ │
│ ┌─────────────┐ │  │                         │ │  │  - 样式配置             │ │
│ │ 业务组件    │ │  │                         │ │  │  - 事件配置             │ │
│ │ - 顶部导航  │ │  │                         │ │  │                         │ │
│ │ - 侧边导航  │ │  │                         │ │  └─────────────────────────┘ │
│ │ - 表格视图  │ │  │                         │ │                             │
│ │ - 状态栏    │ │  │                         │ │  ┌─────────────────────────┐ │
│ └─────────────┘ │  │                         │ │  │                         │ │
│                 │  │                         │ │  │       API配置区域       │ │
│ ┌─────────────┐ │  │                         │ │  │                         │ │
│ │ 页面模板    │ │  │                         │ │  │  - API列表              │ │
│ │ - API管理   │ │  │                         │ │  │  - 参数配置             │ │
│ │ - 用户管理  │ │  │                         │ │  │  - 响应配置             │ │
│ │ - 数据监控  │ │  │                         │ │  │  - 测试工具             │ │
│ └─────────────┘ │  └─────────────────────────┘ │  │                         │ │
│                 │                             │  └─────────────────────────┘ │
├─────────────────┴─────────────────────────────┴─────────────────────────────┤
│                              底部状态栏                                      │
│  当前页面：API管理系统    组件数量：4    最后保存：2025-01-20 10:30:25        │
└─────────────────────────────────────────────────────────────────────────────┘
```

## 2. 业务组件面板

### 2.1 布局组件分类
```typescript
const layoutComponents = [
  {
    category: "布局组件",
    items: [
      {
        type: "admin-layout",
        name: "管理后台布局",
        icon: "layout",
        description: "包含顶部导航、侧边栏、内容区、状态栏的标准后台布局"
      },
      {
        type: "simple-layout", 
        name: "简单布局",
        icon: "layout-simple",
        description: "只包含内容区的简单布局"
      }
    ]
  }
];
```

### 2.2 业务组件分类
```typescript
const businessComponents = [
  {
    category: "导航组件",
    items: [
      {
        type: "TopNavigation",
        name: "顶部导航",
        icon: "menu",
        description: "顶部主导航菜单，支持Logo、菜单项、用户信息"
      },
      {
        type: "SidebarTreeView", 
        name: "侧边树导航",
        icon: "tree",
        description: "树形侧边导航，支持搜索、计数、多级展开"
      }
    ]
  },
  {
    category: "数据组件", 
    items: [
      {
        type: "TableViewWithSearch",
        name: "表格视图",
        icon: "table",
        description: "集成搜索、工具栏、表格、分页的完整数据展示组件"
      },
      {
        type: "ChartView",
        name: "图表视图", 
        icon: "chart",
        description: "数据图表展示组件"
      }
    ]
  },
  {
    category: "其他组件",
    items: [
      {
        type: "StatusBar",
        name: "状态栏",
        icon: "status",
        description: "底部状态栏，显示版权、版本等信息"
      }
    ]
  }
];
```

### 2.3 页面模板
```typescript
const pageTemplates = [
  {
    name: "API管理页面",
    thumbnail: "/templates/api-management.png",
    description: "API监控和管理页面模板",
    schema: "api-management-template.json"
  },
  {
    name: "用户管理页面", 
    thumbnail: "/templates/user-management.png",
    description: "用户列表和编辑页面模板",
    schema: "user-management-template.json"
  },
  {
    name: "数据监控页面",
    thumbnail: "/templates/data-monitor.png", 
    description: "数据监控和统计页面模板",
    schema: "data-monitor-template.json"
  }
];
```

## 3. 画布区域设计

### 3.1 画布交互功能
```typescript
interface CanvasInteraction {
  // 组件选择
  selectComponent: (componentId: string) => void;
  
  // 组件拖拽
  dragComponent: (componentType: string, position: Position) => void;
  
  // 组件删除
  deleteComponent: (componentId: string) => void;
  
  // 组件复制
  copyComponent: (componentId: string) => void;
  
  // 组件移动
  moveComponent: (componentId: string, newPosition: Position) => void;
  
  // 布局切换
  switchLayout: (layoutType: string) => void;
}
```

### 3.2 画布状态管理
```typescript
interface CanvasState {
  // 当前页面Schema
  schema: PageSchema;
  
  // 选中的组件
  selectedComponent: string | null;
  
  // 预览模式
  previewMode: boolean;
  
  // 缩放比例
  scale: number;
  
  // 画布尺寸
  canvasSize: {
    width: number;
    height: number;
  };
}
```

## 4. 配置面板设计

### 4.1 组件配置区域
```typescript
const ComponentConfigPanel: React.FC<{
  component: ComponentInstance;
  onChange: (props: any) => void;
}> = ({ component, onChange }) => {
  const [activeTab, setActiveTab] = useState('basic');
  
  return (
    <div className="component-config-panel">
      <Tabs activeKey={activeTab} onChange={setActiveTab}>
        <TabPane tab="基础属性" key="basic">
          <BasicPropsEditor 
            component={component}
            onChange={onChange}
          />
        </TabPane>
        
        <TabPane tab="数据配置" key="data">
          <DataConfigEditor
            component={component} 
            onChange={onChange}
          />
        </TabPane>
        
        <TabPane tab="样式配置" key="style">
          <StyleConfigEditor
            component={component}
            onChange={onChange} 
          />
        </TabPane>
        
        <TabPane tab="事件配置" key="events">
          <EventConfigEditor
            component={component}
            onChange={onChange}
          />
        </TabPane>
      </Tabs>
    </div>
  );
};
```

### 4.2 TableViewWithSearch 配置界面
```typescript
const TableViewConfigEditor: React.FC = () => {
  return (
    <div className="table-view-config">
      {/* 基础信息 */}
      <FormSection title="基础信息">
        <Form.Item label="标题">
          <Input placeholder="请输入表格标题" />
        </Form.Item>
        <Form.Item label="副标题">
          <Input placeholder="请输入副标题" />
        </Form.Item>
      </FormSection>
      
      {/* 搜索配置 */}
      <FormSection title="搜索配置">
        <SearchFieldsEditor />
      </FormSection>
      
      {/* 工具栏配置 */}
      <FormSection title="工具栏配置">
        <ToolbarEditor />
      </FormSection>
      
      {/* 表格配置 */}
      <FormSection title="表格配置">
        <TableColumnsEditor />
      </FormSection>
      
      {/* 数据源配置 */}
      <FormSection title="数据源">
        <DataSourceEditor />
      </FormSection>
    </div>
  );
};
```

## 5. API配置区域

### 5.1 API管理界面
```typescript
const ApiConfigPanel: React.FC = () => {
  const [apis, setApis] = useState<ApiConfig[]>([]);
  const [selectedApi, setSelectedApi] = useState<string | null>(null);
  
  return (
    <div className="api-config-panel">
      <div className="api-list">
        <div className="api-list-header">
          <h3>API列表</h3>
          <Button type="primary" onClick={addNewApi}>
            新增API
          </Button>
        </div>
        
        <List
          dataSource={apis}
          renderItem={(api) => (
            <List.Item
              className={selectedApi === api.name ? 'selected' : ''}
              onClick={() => setSelectedApi(api.name)}
              actions={[
                <Button type="link" onClick={() => testApi(api)}>
                  测试
                </Button>,
                <Button type="link" onClick={() => deleteApi(api.name)}>
                  删除
                </Button>
              ]}
            >
              <List.Item.Meta
                title={api.name}
                description={`${api.method} ${api.url}`}
              />
            </List.Item>
          )}
        />
      </div>
      
      {selectedApi && (
        <div className="api-editor">
          <ApiEditor 
            api={apis.find(a => a.name === selectedApi)}
            onChange={updateApi}
          />
        </div>
      )}
    </div>
  );
};
```

### 5.2 API测试工具
```typescript
const ApiTester: React.FC<{ api: ApiConfig }> = ({ api }) => {
  const [testParams, setTestParams] = useState({});
  const [testResult, setTestResult] = useState(null);
  const [testing, setTesting] = useState(false);
  
  return (
    <div className="api-tester">
      <h4>API测试</h4>
      
      <Form layout="vertical">
        <Form.Item label="请求URL">
          <Input value={api.url} disabled />
        </Form.Item>
        
        <Form.Item label="请求方法">
          <Select value={api.method} disabled>
            <Option value="GET">GET</Option>
            <Option value="POST">POST</Option>
            <Option value="PUT">PUT</Option>
            <Option value="DELETE">DELETE</Option>
          </Select>
        </Form.Item>
        
        <Form.Item label="测试参数">
          <JsonEditor
            value={testParams}
            onChange={setTestParams}
          />
        </Form.Item>
        
        <Form.Item>
          <Button 
            type="primary" 
            loading={testing}
            onClick={handleTest}
          >
            发送测试请求
          </Button>
        </Form.Item>
      </Form>
      
      {testResult && (
        <div className="test-result">
          <h5>响应结果</h5>
          <JsonViewer data={testResult} />
        </div>
      )}
    </div>
  );
};
```

## 6. 设计器核心功能

### 6.1 拖拽系统
```typescript
const DragDropSystem = {
  // 从组件面板拖拽到画布
  handleComponentDrop: (componentType: string, dropPosition: Position) => {
    const newComponent = createComponent(componentType, dropPosition);
    addComponentToSchema(newComponent);
    selectComponent(newComponent.id);
  },
  
  // 画布内组件移动
  handleComponentMove: (componentId: string, newPosition: Position) => {
    updateComponentPosition(componentId, newPosition);
  },
  
  // 组件排序
  handleComponentReorder: (dragIndex: number, hoverIndex: number) => {
    reorderComponents(dragIndex, hoverIndex);
  }
};
```

### 6.2 实时预览
```typescript
const PreviewSystem = {
  // 切换预览模式
  togglePreview: () => {
    setPreviewMode(!previewMode);
  },
  
  // 实时渲染
  renderPreview: (schema: PageSchema) => {
    return <Renderer schema={schema} />;
  },
  
  // 响应式预览
  previewInDifferentSizes: (size: 'desktop' | 'tablet' | 'mobile') => {
    setPreviewSize(size);
  }
};
```
