<!DOCTYPE html>
<html lang="en"><head>
<meta charset="utf-8"/>
<meta content="width=device-width, initial-scale=1.0" name="viewport"/>
<title><PERSON>uan Z<PERSON></title>
<link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet"/>
<link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet"/>
</head>
<body class="bg-gray-100">
<header class="bg-gray-800 text-white">
<div class="container mx-auto px-4">
<div class="flex items-center justify-between h-16">
<div class="flex items-center">
<img alt="Quan Zhi Tech logo" class="h-8 mr-4" src="https://lh3.googleusercontent.com/aida-public/AB6AXuDJlIertYxJwKyZni2TqSEgYYKd9D4eWVxMcQN-wrQc4vfgfvuKM43ZbkblYktAnmSscBOydbL-rv7M-M0OSiwVGeQeWRI7DmYFw7c_-Dx8cEV_s8nk5Nb7WtA9xZLDEHN2poJCkswu1C37P4jsepUv1ZxIpqoll05EkMg-jR-yMWmt9mhnhRvUiv3bMJppTt6FkzXugMAUmCvu513fXupZmxq3Msrj695fEfWPeUX5scDLel8fE7J3Pgb-IRuBLrRdpAq7qBBV51s"/>
<nav class="hidden md:flex items-center space-x-1">
<a class="px-3 py-2 rounded-md text-sm font-medium text-gray-300 hover:bg-gray-700" href="#">概览</a>
<a class="px-3 py-2 rounded-md text-sm font-medium bg-blue-500 text-white" href="#">APIs</a>
<a class="px-3 py-2 rounded-md text-sm font-medium text-gray-300 hover:bg-gray-700" href="#">数据</a>
<a class="px-3 py-2 rounded-md text-sm font-medium text-gray-300 hover:bg-gray-700" href="#">弱点</a>
<a class="px-3 py-2 rounded-md text-sm font-medium text-gray-300 hover:bg-gray-700" href="#">风控</a>
<a class="px-3 py-2 rounded-md text-sm font-medium text-gray-300 hover:bg-gray-700" href="#">审计</a>
<a class="px-3 py-2 rounded-md text-sm font-medium text-gray-300 hover:bg-gray-700" href="#">报告</a>
<a class="px-3 py-2 rounded-md text-sm font-medium text-gray-300 hover:bg-gray-700" href="#">态势</a>
<a class="px-3 py-2 rounded-md text-sm font-medium text-gray-300 hover:bg-gray-700" href="#">管控</a>
<a class="px-3 py-2 rounded-md text-sm font-medium text-gray-300 hover:bg-gray-700" href="#">配置</a>
</nav>
</div>
<div class="flex items-center space-x-4">
<button class="text-gray-300 hover:text-white">
<i class="material-icons">search</i>
</button>
<div class="relative">
<button class="text-gray-300 hover:text-white">
<i class="material-icons">notifications</i>
</button>
<span class="absolute -top-1 -right-2 bg-red-500 text-white text-xs rounded-full h-4 w-4 flex items-center justify-center">5</span>
</div>
<div class="relative">
<button class="flex items-center text-gray-300 hover:text-white">
<i class="material-icons">book</i>
<span class="ml-1">book</span>
<i class="material-icons">arrow_drop_down</i>
</button>
</div>
</div>
</div>
</div>
</header>
<main class="bg-white">
<div class="container mx-auto px-4 py-3 border-b">
<div class="flex items-center space-x-4">
<div class="flex items-center text-blue-500">
<span class="material-icons text-xl">api</span>
<span class="ml-1 font-semibold">API</span>
</div>
<div class="flex items-center text-gray-600">
<span class="material-icons text-xl">cloud_upload</span>
<span class="ml-1">应用</span>
</div>
</div>
</div>
</main>

</body></html>