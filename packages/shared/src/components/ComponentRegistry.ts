import React from 'react';
import { ComponentMeta } from '../types';

export class ComponentRegistry {
  private components: Map<string, React.ComponentType<any>> = new Map();
  private metas: Map<string, ComponentMeta> = new Map();

  // 注册组件
  register(meta: ComponentMeta, component?: React.ComponentType<any>) {
    this.metas.set(meta.type, meta);

    if (component) {
      this.components.set(meta.type, component);
    }
  }

  // 获取组件
  getComponent(type: string): React.ComponentType<any> | undefined {
    return this.components.get(type);
  }

  // 获取组件元数据
  getMeta(type: string): ComponentMeta | undefined {
    return this.metas.get(type);
  }

  // 获取所有组件类型
  getComponentTypes(): string[] {
    return Array.from(this.components.keys());
  }

  // 获取所有组件元数据
  getAllMetas(): ComponentMeta[] {
    return Array.from(this.metas.values());
  }

  // 按分类获取组件
  getComponentsByCategory(category: string): ComponentMeta[] {
    return Array.from(this.metas.values()).filter(meta => meta.category === category);
  }

  // 检查组件是否已注册
  hasComponent(type: string): boolean {
    return this.components.has(type);
  }

  // 注销组件
  unregister(type: string) {
    this.components.delete(type);
    this.metas.delete(type);
  }

  // 清空所有组件
  clear() {
    this.components.clear();
    this.metas.clear();
  }

  // 获取组件数量
  size(): number {
    return this.components.size;
  }
}
