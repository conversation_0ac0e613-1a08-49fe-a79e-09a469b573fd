# 渲染器实现方案

## 1. 渲染器核心架构

### 1.1 主渲染器
```typescript
const Renderer: React.FC<{ schema: PageSchema }> = ({ schema }) => {
  const { layout, components, apis, theme } = schema;
  
  useEffect(() => {
    // 初始化主题
    themeManager.setTheme(theme);
    
    // 注册APIs
    Object.entries(apis).forEach(([name, config]) => {
      apiManager.register(name, config);
    });
  }, [theme, apis]);
  
  // 根据布局类型渲染
  const renderLayout = () => {
    switch (layout.type) {
      case 'topdown-layout':
        return <TopDownLayoutRenderer layout={layout} components={components} />;
      case 'simple-layout':
        return <SimpleLayoutRenderer layout={layout} components={components} />;
      default:
        return <div>Unsupported layout type: {layout.type}</div>;
    }
  };
  
  return (
    <div className="lowcode-renderer">
      {renderLayout()}
    </div>
  );
};
```

### 1.2 TopDownLayout 渲染器
```typescript
const TopDownLayoutRenderer: React.FC<{
  layout: TopDownLayoutConfig;
  components: Record<string, ComponentConfig>;
}> = ({ layout, components }) => {
  const topNavComponent = components[layout.components.topNavigation];
  const sidebarComponent = components[layout.components.sidebar];
  const contentComponent = components[layout.components.content];
  const statusBarComponent = components[layout.components.statusBar];

  return (
    <Layout className="topdown-layout">
      <Layout.Header className="topdown-header">
        <ComponentRenderer component={topNavComponent} />
      </Layout.Header>

      <Layout className="topdown-body">
        <Layout.Sider className="topdown-sidebar" width={280}>
          <ComponentRenderer component={sidebarComponent} />
        </Layout.Sider>

        <Layout.Content className="topdown-content">
          <ComponentRenderer component={contentComponent} />
        </Layout.Content>
      </Layout>

      <Layout.Footer className="topdown-footer">
        <ComponentRenderer component={statusBarComponent} />
      </Layout.Footer>
    </Layout>
  );
};
```

## 2. 业务组件实现

### 2.1 TopNavigation 组件
```typescript
const TopNavigation: React.FC<TopNavigationProps> = ({
  logo,
  menus,
  userInfo
}) => {
  const [selectedMenu, setSelectedMenu] = useState(
    menus.find(m => m.active)?.key || menus[0]?.key
  );
  
  const handleMenuClick = (menuKey: string) => {
    setSelectedMenu(menuKey);
    // 触发页面切换事件
    eventBus.emit('menuChange', menuKey);
  };
  
  return (
    <div className="top-navigation">
      <div className="nav-left">
        <div className="logo">
          {logo.icon && <img src={logo.icon} alt="logo" />}
          <span className="logo-text">{logo.text}</span>
        </div>
        
        <div className="nav-menus">
          {menus.map(menu => (
            <div
              key={menu.key}
              className={`nav-menu ${selectedMenu === menu.key ? 'active' : ''}`}
              onClick={() => handleMenuClick(menu.key)}
            >
              {menu.label}
            </div>
          ))}
        </div>
      </div>
      
      <div className="nav-right">
        <div className="user-info">
          {userInfo.showNotification && (
            <Badge count={userInfo.notificationCount || 0}>
              <BellOutlined className="notification-icon" />
            </Badge>
          )}
          
          <Dropdown
            overlay={
              <Menu>
                <Menu.Item key="profile">个人资料</Menu.Item>
                <Menu.Item key="settings">设置</Menu.Item>
                <Menu.Divider />
                <Menu.Item key="logout">退出登录</Menu.Item>
              </Menu>
            }
          >
            <div className="user-avatar">
              {userInfo.avatar ? (
                <Avatar src={userInfo.avatar} />
              ) : (
                <Avatar>{userInfo.name[0]}</Avatar>
              )}
              <span className="user-name">{userInfo.name}</span>
              <DownOutlined />
            </div>
          </Dropdown>
        </div>
      </div>
    </div>
  );
};
```

### 2.2 SidebarTreeView 组件
```typescript
const SidebarTreeView: React.FC<SidebarTreeViewProps> = ({
  title,
  searchable,
  searchPlaceholder,
  tree,
  defaultSelected,
  dataBinding
}) => {
  const [treeData, setTreeData] = useState(tree);
  const [selectedKeys, setSelectedKeys] = useState([defaultSelected]);
  const [searchValue, setSearchValue] = useState('');
  const [expandedKeys, setExpandedKeys] = useState<string[]>([]);
  
  // 加载树数据
  useEffect(() => {
    if (dataBinding?.source) {
      loadTreeData();
    }
  }, [dataBinding]);
  
  const loadTreeData = async () => {
    try {
      const data = await apiManager.call(dataBinding.source);
      setTreeData(data);
      
      // 自动展开第一级
      const firstLevelKeys = data.map((node: TreeNode) => node.key);
      setExpandedKeys(firstLevelKeys);
    } catch (error) {
      message.error('加载数据失败');
    }
  };
  
  const handleSelect = (selectedKeys: string[]) => {
    setSelectedKeys(selectedKeys);
    
    // 触发选择事件
    if (selectedKeys.length > 0) {
      eventBus.emit('treeNodeSelect', {
        key: selectedKeys[0],
        node: findNodeByKey(treeData, selectedKeys[0])
      });
    }
  };
  
  const handleSearch = (value: string) => {
    setSearchValue(value);
    
    if (value) {
      // 过滤树节点
      const filteredData = filterTreeData(treeData, value);
      setTreeData(filteredData);
      
      // 展开所有匹配的节点
      const allKeys = getAllKeys(filteredData);
      setExpandedKeys(allKeys);
    } else {
      // 重置数据
      loadTreeData();
    }
  };
  
  const renderTreeNode = (node: TreeNode) => (
    <Tree.TreeNode
      key={node.key}
      title={
        <div className="tree-node-title">
          {node.icon && <Icon type={node.icon} />}
          <span className="node-text">{node.title}</span>
          {node.count !== undefined && (
            <span className="node-count">{node.count}</span>
          )}
        </div>
      }
    >
      {node.children?.map(renderTreeNode)}
    </Tree.TreeNode>
  );
  
  return (
    <div className="sidebar-tree-view">
      <div className="tree-header">
        <h3 className="tree-title">{title}</h3>
        
        {searchable && (
          <Input.Search
            placeholder={searchPlaceholder}
            value={searchValue}
            onChange={(e) => handleSearch(e.target.value)}
            style={{ marginTop: 8 }}
          />
        )}
      </div>
      
      <div className="tree-content">
        <Tree
          selectedKeys={selectedKeys}
          expandedKeys={expandedKeys}
          onSelect={handleSelect}
          onExpand={setExpandedKeys}
          showLine
        >
          {treeData.map(renderTreeNode)}
        </Tree>
      </div>
    </div>
  );
};
```

### 2.3 TableViewWithSearch 组件
```typescript
const TableViewWithSearch: React.FC<TableViewWithSearchProps> = ({
  title,
  subtitle,
  searchConfig,
  toolbar,
  table,
  summary,
  dataBinding
}) => {
  const [searchForm] = Form.useForm();
  const [tableData, setTableData] = useState([]);
  const [loading, setLoading] = useState(false);
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: table.pagination.pageSize,
    total: 0
  });
  const [selectedRowKeys, setSelectedRowKeys] = useState<string[]>([]);
  
  // 加载表格数据
  const loadTableData = useCallback(async (params = {}) => {
    if (!dataBinding?.source) return;
    
    setLoading(true);
    try {
      const searchValues = searchForm.getFieldsValue();
      const requestParams = {
        ...dataBinding.params,
        ...searchValues,
        ...params,
        page: pagination.current,
        pageSize: pagination.pageSize
      };
      
      const response = await apiManager.call(dataBinding.source, requestParams);
      setTableData(response.list || response.data || []);
      setPagination(prev => ({
        ...prev,
        total: response.total || response.count || 0
      }));
    } catch (error) {
      message.error('数据加载失败');
    } finally {
      setLoading(false);
    }
  }, [dataBinding, pagination.current, pagination.pageSize]);
  
  // 初始加载
  useEffect(() => {
    if (dataBinding?.autoLoad) {
      loadTableData();
    }
  }, [loadTableData, dataBinding?.autoLoad]);
  
  // 监听外部事件
  useEffect(() => {
    const handleRefresh = () => loadTableData();
    const handleTreeSelect = (data: any) => {
      // 根据树选择更新表格数据
      loadTableData({ category: data.key });
    };
    
    eventBus.on('refreshTable', handleRefresh);
    eventBus.on('treeNodeSelect', handleTreeSelect);
    
    return () => {
      eventBus.off('refreshTable', handleRefresh);
      eventBus.off('treeNodeSelect', handleTreeSelect);
    };
  }, [loadTableData]);
  
  // 搜索处理
  const handleSearch = (values: any) => {
    setPagination(prev => ({ ...prev, current: 1 }));
    loadTableData(values);
  };
  
  // 重置搜索
  const handleReset = () => {
    searchForm.resetFields();
    setPagination(prev => ({ ...prev, current: 1 }));
    loadTableData();
  };
  
  // 分页变化
  const handleTableChange = (paginationConfig: any) => {
    setPagination(prev => ({
      ...prev,
      current: paginationConfig.current,
      pageSize: paginationConfig.pageSize
    }));
  };
  
  // 行选择
  const rowSelection = table.selection ? {
    type: table.selection.type,
    selectedRowKeys,
    onChange: setSelectedRowKeys,
    showSelectAll: table.selection.showSelectAll
  } : undefined;
  
  return (
    <div className="table-view-with-search">
      {/* 标题区域 */}
      <div className="table-header">
        <div className="table-title">
          <h2>{title}</h2>
          {subtitle && <span className="table-subtitle">{subtitle}</span>}
        </div>
      </div>
      
      {/* 搜索区域 */}
      <div className="search-section">
        <SearchForm
          form={searchForm}
          config={searchConfig}
          onSearch={handleSearch}
          onReset={handleReset}
        />
      </div>
      
      {/* 工具栏区域 */}
      <div className="toolbar-section">
        <ToolbarRenderer
          config={toolbar}
          selectedRowKeys={selectedRowKeys}
          onAction={(action, data) => {
            // 处理工具栏操作
            handleToolbarAction(action, data);
          }}
        />
      </div>
      
      {/* 表格区域 */}
      <div className="table-section">
        <Table
          columns={table.columns.map(col => ({
            ...col,
            render: (value, record, index) => 
              renderTableCell(col, value, record, index)
          }))}
          dataSource={tableData}
          loading={loading}
          pagination={{
            ...pagination,
            showSizeChanger: table.pagination.showSizeChanger,
            showQuickJumper: table.pagination.showQuickJumper,
            showTotal: table.pagination.showTotal ? 
              (total, range) => `共 ${total} 条，第 ${range[0]}-${range[1]} 条` : 
              undefined
          }}
          rowSelection={rowSelection}
          onChange={handleTableChange}
          rowKey="id"
        />
      </div>
      
      {/* 汇总区域 */}
      {summary?.show && (
        <div className="summary-section">
          <span>{summary.template}</span>
        </div>
      )}
    </div>
  );
};
```

### 2.4 StatusBar 组件
```typescript
const StatusBar: React.FC<StatusBarProps> = ({ left, right }) => {
  return (
    <div className="status-bar">
      <div className="status-left">
        <span>{left.copyright}</span>
      </div>
      
      <div className="status-right">
        <span>{right.version}</span>
      </div>
    </div>
  );
};
```

## 3. 辅助组件实现

### 3.1 SearchForm 组件
```typescript
const SearchForm: React.FC<{
  form: FormInstance;
  config: SearchConfig;
  onSearch: (values: any) => void;
  onReset: () => void;
}> = ({ form, config, onSearch, onReset }) => {
  const handleFinish = (values: any) => {
    onSearch(values);
  };
  
  return (
    <Form
      form={form}
      layout={config.layout}
      onFinish={handleFinish}
      className="search-form"
    >
      <Row gutter={16}>
        {config.fields.map(field => (
          <Col key={field.name} span={6}>
            <Form.Item name={field.name} label={field.label}>
              <SearchFieldRenderer field={field} />
            </Form.Item>
          </Col>
        ))}
        
        <Col span={6}>
          <Form.Item>
            <Space>
              {config.showSearch && (
                <Button type="primary" htmlType="submit">
                  搜索
                </Button>
              )}
              {config.showReset && (
                <Button onClick={onReset}>
                  重置
                </Button>
              )}
            </Space>
          </Form.Item>
        </Col>
      </Row>
    </Form>
  );
};
```

### 3.2 ToolbarRenderer 组件
```typescript
const ToolbarRenderer: React.FC<{
  config: ToolbarConfig;
  selectedRowKeys: string[];
  onAction: (action: string, data: any) => void;
}> = ({ config, selectedRowKeys, onAction }) => {
  return (
    <div className="toolbar">
      <div className="toolbar-left">
        {config.left.map((item, index) => (
          <ToolbarItemRenderer
            key={index}
            item={item}
            onAction={onAction}
          />
        ))}
      </div>
      
      <div className="toolbar-right">
        {config.right.map((item, index) => (
          <ToolbarItemRenderer
            key={index}
            item={item}
            onAction={onAction}
          />
        ))}
      </div>
    </div>
  );
};
```

## 4. 组件注册系统

```typescript
class ComponentRegistry {
  private components: Map<string, React.ComponentType> = new Map();
  
  constructor() {
    // 注册内置组件
    this.registerBuiltinComponents();
  }
  
  private registerBuiltinComponents() {
    this.register('TopNavigation', TopNavigation);
    this.register('SidebarTreeView', SidebarTreeView);
    this.register('TableViewWithSearch', TableViewWithSearch);
    this.register('StatusBar', StatusBar);
  }
  
  register(type: string, component: React.ComponentType) {
    this.components.set(type, component);
  }
  
  get(type: string): React.ComponentType | undefined {
    return this.components.get(type);
  }
  
  getAll(): string[] {
    return Array.from(this.components.keys());
  }
}

export const componentRegistry = new ComponentRegistry();
```
