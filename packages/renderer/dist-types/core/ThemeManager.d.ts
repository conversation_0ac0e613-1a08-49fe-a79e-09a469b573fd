import { ThemeConfig } from '../types';
export declare class ThemeManager {
    private theme;
    private defaultTheme;
    private presetThemes;
    constructor(initialTheme?: ThemeConfig);
    getTheme(): ThemeConfig;
    getThemeVar(key: keyof ThemeConfig): any;
    resetTheme(): void;
    private applyTheme;
    getPageStyle(): React.CSSProperties;
    getComponentStyle(customStyle?: React.CSSProperties): React.CSSProperties;
    generateThemeCSS(): string;
    exportTheme(): string;
    importTheme(themeJson: string): void;
    applyPresetTheme(presetName: string): void;
    getPresetThemeNames(): string[];
    getPresetTheme(name: string): ThemeConfig | undefined;
    addPresetTheme(name: string, theme: ThemeConfig): void;
    removePresetTheme(name: string): void;
    getThemeDiff(presetName: string): Partial<ThemeConfig>;
    createThemeVariant(adjustments: Partial<ThemeConfig>): ThemeConfig;
    getCSSVariables(): Record<string, string>;
    setCSSVariables(variables: Record<string, string>): void;
    private themeChangeListeners;
    onThemeChange(listener: (theme: ThemeConfig) => void): void;
    offThemeChange(listener: (theme: ThemeConfig) => void): void;
    private notifyThemeChange;
    setTheme(theme: ThemeConfig): void;
}
//# sourceMappingURL=ThemeManager.d.ts.map