import React, { useEffect, useState, useCallback } from 'react';
import type { PageSchema } from '@lowcode/renderer';
import { DesignerProvider, Canvas, PropertyDrawer, Toolbar, useDesigner, DesignComponentSidebar } from '@lowcode/designer';

// API 管理页面 Schema（接近截图效果）
const apiPageSchema: PageSchema = {
  id: 'api_management',
  title: '全部API',
  components: [
    { id: 'top_nav', type: 'TopNavigation', props: {
        logo: { 
          src:"",
          href: '/' },
        menu: [
          { key: 'overview', label: '概览', href: '/overview' },
          { key: 'apis', label: 'APIs', href: '/apis' },
          { key: 'data', label: '数据', href: '/data' },
          { key: 'insight', label: '洞察', href: '/insight' },
          { key: 'risk', label: '风险', href: '/risk' },
          { key: 'audit', label: '审计', href: '/audit' },
          { key: 'config', label: '配置', href: '/config' }
        ],
        user: { name: 'book', menu: [{ key: 'logout', label: '退出' }] }
    }},
    { id: 'main', type: 'Container', props: { style: { display: 'flex', minHeight: 'calc(100vh - 96px)' } },
      children: [
        { id: 'left_sidebar', type: 'Container', props: { style: { width: '240px' } },
          children: [
            { id: 'sidebar', type: 'SidebarTreeView', props: {
                width: 240, height: '100%', searchable: true, searchPlaceholder: '搜索视图/分类',
                data: [
                  { key: 'all', title: '全部API', count: 212, href: '/apis/all' },
                  {
                    key: 'penetration',
                    title: '渗透测试重点API',
                    children: [
                      { key: 'login', title: '登录API', count: 26 },
                      { key: 'url', title: 'URL重定向API', count: 5 },
                      { key: 'response', title: '单次响应数据量过大...', count: 3 },
                      { key: 'sms', title: '短信验证码发送API', count: 2 },
                      { key: 'register', title: '注册API', count: 0 }
                    ]
                  },
                  { key: 'network', title: '互联网敏感API' },
                  { key: 'format', title: 'API格式' },
                  { key: 'label', title: 'API标签' }
                ]
            }}
          ]
        },
        { id: 'content', type: 'Container', props: { style: { flex: 1, display: 'flex', flexDirection: 'column' } },
          children: [
            { id: 'api_table', type: 'TableViewWithSearch',
              mockData: JSON.stringify([
                { id: 1, path: '/login', sensitivity: '高敏感', riskLevel: '高风险', totalVisits: '3.6千', trafficSource: '192.168.0.1', firstSeen: '2025-08-14 19:18:21' },
                { id: 2, path: '/abnormal', sensitivity: '高敏感', riskLevel: '高风险', totalVisits: '2.5千', trafficSource: '192.168.0.1', firstSeen: '2025-08-14 19:19:12' },
                { id: 3, path: '/api/users', sensitivity: '中敏感', riskLevel: '中风险', totalVisits: '1.2千', trafficSource: '192.168.0.2', firstSeen: '2025-08-14 20:15:33' },
                { id: 4, path: '/api/posts', sensitivity: '低敏感', riskLevel: '低风险', totalVisits: '800', trafficSource: '192.168.0.3', firstSeen: '2025-08-14 21:22:45' }
              ], null, 2),
              props: {
                title: '全部API',
                searchFields: [
                  { key: 'application', label: '应用', type: 'input', placeholder: '请输入' },
                  { key: 'sensitivityLevel', label: 'API敏感等级', type: 'select', placeholder: '请选择', options: [
                    { label: '高敏感', value: 'high' }, { label: '中敏感', value: 'medium' }, { label: '低敏感', value: 'low' }
                  ]},
                  { key: 'applicationName', label: '应用名称', type: 'input', placeholder: '请输入' },
                  { key: 'apiTags', label: 'API标签', type: 'combinedSelect', placeholder: '请选择', combinedConfig: {
                    leftOptions: [{ label: '或', value: 'or' }, { label: '且', value: 'and' }],
                    rightOptions: [{ label: '标签1', value: 'tag1' }, { label: '标签2', value: 'tag2' }]
                  }},
                  { key: 'accessDomain', label: '访问域', type: 'combinedSelect', placeholder: '请选择', combinedConfig: {
                    leftOptions: [{ label: '或', value: 'or' }],
                    rightOptions: [{ label: '域名1', value: 'domain1' }, { label: '域名2', value: 'domain2' }]
                  }},
                  { key: 'deployDomain', label: '部署域', type: 'combinedSelect', placeholder: '请选择', combinedConfig: {
                    leftOptions: [{ label: '或', value: 'or' }],
                    rightOptions: [{ label: '部署域1', value: 'deploy1' }, { label: '部署域2', value: 'deploy2' }]
                  }},
                  { key: 'terminalType', label: '终端类型', type: 'combinedSelect', placeholder: '请选择', combinedConfig: {
                    leftOptions: [{ label: '或', value: 'or' }],
                    rightOptions: [{ label: 'Web', value: 'web' }, { label: 'Mobile', value: 'mobile' }]
                  }},
                  { key: 'requestType', label: '请求类型', type: 'select', placeholder: '请选择', options: [
                    { label: 'GET', value: 'get' }, { label: 'POST', value: 'post' }, { label: 'PUT', value: 'put' }, { label: 'DELETE', value: 'delete' }
                  ]},
                  { key: 'responseType', label: '响应类型', type: 'select', placeholder: '请选择', options: [
                    { label: 'JSON', value: 'json' }, { label: 'XML', value: 'xml' }, { label: 'HTML', value: 'html' }
                  ]},
                  { key: 'responseDataTags', label: '响应数据标签', type: 'checkbox', placeholder: '请选择',
                    options: [{ label: '标签A', value: 'tagA' }, { label: '标签B', value: 'tagB' }],
                    checkboxConfig: { text: '按单例/桶', tooltip: '提示信息' }
                  },
                  { key: 'discoveryTime', label: '首次发现时间', type: 'select', placeholder: '请选择', options: [
                    { label: '今天', value: 'today' }, { label: '昨天', value: 'yesterday' }, { label: '最近7天', value: 'week' }, { label: '最近30天', value: 'month' }
                  ]}
                ],
                showMoreSearchConditions: true,
                quickSearch: { placeholder: '请输入URL进行筛选' },
                toolbarActions: [
                  { key: 'batch', label: '批量操作', type: 'primary' },
                  { key: 'saveView', label: '保存视图', type: 'default' }
                ],
                toolbarIcons: [
                  { key: 'filter', icon: 'filter_alt', tooltip: '筛选' },
                  { key: 'refresh', icon: 'refresh', tooltip: '刷新' },
                  { key: 'columns', icon: 'view_column', tooltip: '列设置' }
                ],
                groupTabs: [
                  { key: 'high', label: '高风险', count: 34, active: true },
                  { key: 'medium', label: '中风险', count: 38, active: false },
                  { key: 'low', label: '低风险', count: 8, active: false },
                  { key: 'none', label: '无风险', count: 124, active: false },
                  { key: 'other', label: '其他', count: 8, active: false }
                ],
                showCancelGroup: true,
                columns: [
                  { key: 'path', title: '路径', dataIndex: 'path', width: '33%' },
                  { key: 'sensitivity', title: 'API敏感等级', dataIndex: 'sensitivity', width: '16%' },
                  { key: 'riskLevel', title: 'API风险等级', dataIndex: 'riskLevel', width: '16%' },
                  { key: 'totalVisits', title: '累计访问次数', dataIndex: 'totalVisits', width: '16%', sortable: true },
                  { key: 'trafficSource', title: '流量来源', dataIndex: 'trafficSource', width: '16%' },
                  { key: 'firstSeen', title: '首次发现时间', dataIndex: 'firstSeen', width: '25%', sortable: true }
                ],
                rowActions: [
                  { key: 'copy', label: '📋', onClick: () => {} },
                  { key: 'more', label: '⋯', onClick: () => {} }
                ],
                rowSelection: { type: 'checkbox' },
                pagination: { current: 1, pageSize: 25, total: 34, showSizeChanger: true, showTotal: true }
            }}
          ]
        }
      ]
    },
    { id: 'status_bar', type: 'StatusBar', props: { items: [ { key: 'copyright', label: 'Copyright', value: '© 2017-2025 All rights reserved.' }, { key: 'version', label: '版本', value: '3.3.0.20250731' } ] } }
  ],
  apis: [],
  theme: undefined as any
};


// 设计器内部桥接：把设计器上下文中的 schema 同步回父组件
const SchemaSyncBridge: React.FC<{ onChange: (schema: PageSchema) => void }> = ({ onChange }) => {
  const { schema } = useDesigner();
  useEffect(() => {
    onChange(schema);
  }, [schema, onChange]);
  return null;
};

const App: React.FC = () => {
  // 从localStorage加载保存的schema，如果没有则使用默认的apiPageSchema
  const [currentSchema, setCurrentSchema] = useState<PageSchema>(() => {
    try {
      const savedSchema = localStorage.getItem('lowcode-designer-schema');
      if (savedSchema) {
        const parsed = JSON.parse(savedSchema);
        // 确保解析的数据有必要的字段
        if (parsed && parsed.id && parsed.components) {
          // 检查是否有mockData字段，如果没有则使用默认schema
          const hasTableComponent = parsed.components.some((comp: any) =>
            comp.type === 'TableViewWithSearch' ||
            (comp.children && comp.children.some((child: any) => child.type === 'TableViewWithSearch'))
          );

          if (hasTableComponent) {
            // 检查TableViewWithSearch组件是否有mockData
            const findTableComponent = (components: any[]): any => {
              for (const comp of components) {
                if (comp.type === 'TableViewWithSearch') {
                  return comp;
                }
                if (comp.children) {
                  const found = findTableComponent(comp.children);
                  if (found) return found;
                }
              }
              return null;
            };

            const tableComponent = findTableComponent(parsed.components);
            if (tableComponent && !tableComponent.mockData) {
              // 如果TableViewWithSearch组件没有mockData，使用默认schema
              console.log('TableViewWithSearch component missing mockData, using default schema');
              return apiPageSchema;
            }
          }

          return parsed;
        }
      }
    } catch (error) {
      console.warn('Failed to load saved schema from localStorage:', error);
    }
    return apiPageSchema;
  });

  // 当schema变化时，保存到localStorage
  const handleSchemaChange = useCallback((newSchema: PageSchema) => {
    setCurrentSchema(newSchema);
    try {
      localStorage.setItem('lowcode-designer-schema', JSON.stringify(newSchema));
    } catch (error) {
      console.warn('Failed to save schema to localStorage:', error);
    }
  }, []);




// 左侧组件面板：使用设计稿样式的侧边栏
const CollapsibleComponentPanel: React.FC = () => {
  return <DesignComponentSidebar />;
};

  return (
    <div style={{ height: '100vh', display: 'flex', flexDirection: 'column' }}>
      <DesignerProvider initialSchema={currentSchema}>
        <div style={{ flex: 1, display: 'flex', flexDirection: 'column' }}>
          <Toolbar />
          <div style={{ flex: 1, display: 'flex', position: 'relative', overflow: 'hidden' }}>
            <CollapsibleComponentPanel />
            <div style={{ flex: 1, minWidth: 0 }}>
              <Canvas />
            </div>
            {/* 右侧属性编辑以抽屉覆盖在预览区上方 */}
            <PropertyDrawer />
          </div>
        </div>
        {/* 同步设计器中的 schema 到父组件，从而影响渲染器视图 */}
        <SchemaSyncBridge onChange={handleSchemaChange} />
      </DesignerProvider>
    </div>
  );
};

export default App;
