import React, { useState, useEffect, useRef } from 'react';

export interface JsonEditorProps {
  value?: any;
  onChange: (value: any) => void;
  placeholder?: string;
  height?: number;
  readOnly?: boolean;
}

export const JsonEditor: React.FC<JsonEditorProps> = ({
  value,
  onChange,
  placeholder = '请输入JSON格式数据',
  height = 200,
  readOnly = false
}) => {
  const [text, setText] = useState(() => 
    value !== undefined ? JSON.stringify(value, null, 2) : ''
  );
  const [error, setError] = useState<string | null>(null);
  const [focused, setFocused] = useState(false);
  const textareaRef = useRef<HTMLTextAreaElement>(null);

  // 当外部value变化时同步更新text
  useEffect(() => {
    if (value !== undefined) {
      const newText = JSON.stringify(value, null, 2);
      if (newText !== text) {
        setText(newText);
        setError(null);
      }
    } else if (text !== '') {
      setText('');
      setError(null);
    }
  }, [value]);

  const handleChange = (newText: string) => {
    setText(newText);
    
    if (newText.trim() === '') {
      setError(null);
      onChange(undefined);
      return;
    }

    try {
      const parsed = JSON.parse(newText);
      setError(null);
      onChange(parsed);
    } catch (err) {
      setError('JSON格式错误');
    }
  };

  const formatJson = () => {
    if (text.trim() === '') return;
    
    try {
      const parsed = JSON.parse(text);
      const formatted = JSON.stringify(parsed, null, 2);
      setText(formatted);
      setError(null);
      onChange(parsed);
    } catch (err) {
      setError('JSON格式错误，无法格式化');
    }
  };

  const containerStyle: React.CSSProperties = {
    position: 'relative',
    border: `2px solid ${error ? '#ff4d4f' : focused ? '#1890ff' : '#d9d9d9'}`,
    borderRadius: '8px',
    backgroundColor: '#fff',
    transition: 'border-color 0.3s ease',
    overflow: 'hidden'
  };

  const headerStyle: React.CSSProperties = {
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: '8px 12px',
    backgroundColor: '#fafafa',
    borderBottom: '1px solid #e8e8e8',
    fontSize: '12px',
    color: '#666'
  };

  const textareaStyle: React.CSSProperties = {
    width: '100%',
    height: `${height}px`,
    padding: '12px',
    border: 'none',
    outline: 'none',
    fontFamily: '"Fira Code", "Monaco", "Consolas", "Ubuntu Mono", monospace',
    fontSize: '13px',
    lineHeight: '1.5',
    backgroundColor: 'transparent',
    resize: 'vertical',
    minHeight: '120px'
  };

  const footerStyle: React.CSSProperties = {
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: '8px 12px',
    backgroundColor: '#fafafa',
    borderTop: '1px solid #e8e8e8',
    fontSize: '12px'
  };

  const buttonStyle: React.CSSProperties = {
    padding: '4px 8px',
    border: '1px solid #d9d9d9',
    borderRadius: '4px',
    backgroundColor: '#fff',
    cursor: 'pointer',
    fontSize: '12px',
    color: '#666',
    transition: 'all 0.2s ease'
  };

  const errorStyle: React.CSSProperties = {
    color: '#ff4d4f',
    fontSize: '12px',
    display: 'flex',
    alignItems: 'center',
    gap: '4px'
  };

  const successStyle: React.CSSProperties = {
    color: '#52c41a',
    fontSize: '12px',
    display: 'flex',
    alignItems: 'center',
    gap: '4px'
  };

  const getLineCount = () => text.split('\n').length;
  const getCharCount = () => text.length;

  return (
    <div style={containerStyle}>
      {/* 头部 */}
      <div style={headerStyle}>
        <span>JSON编辑器</span>
        <div style={{ display: 'flex', gap: '8px', alignItems: 'center' }}>
          <span>行: {getLineCount()}</span>
          <span>字符: {getCharCount()}</span>
        </div>
      </div>

      {/* 编辑区域 */}
      <textarea
        ref={textareaRef}
        style={textareaStyle}
        value={text}
        onChange={(e) => handleChange(e.target.value)}
        onFocus={() => setFocused(true)}
        onBlur={() => setFocused(false)}
        placeholder={placeholder}
        readOnly={readOnly}
        spellCheck={false}
      />

      {/* 底部状态栏 */}
      <div style={footerStyle}>
        <div>
          {error ? (
            <div style={errorStyle}>
              <span>⚠</span>
              <span>{error}</span>
            </div>
          ) : text.trim() !== '' ? (
            <div style={successStyle}>
              <span>✓</span>
              <span>JSON格式正确</span>
            </div>
          ) : (
            <span style={{ color: '#999' }}>请输入JSON数据</span>
          )}
        </div>
        
        <div style={{ display: 'flex', gap: '8px' }}>
          <button
            style={buttonStyle}
            onClick={formatJson}
            disabled={readOnly || !!error || text.trim() === ''}
            onMouseEnter={(e) => {
              if (!e.currentTarget.disabled) {
                e.currentTarget.style.borderColor = '#1890ff';
                e.currentTarget.style.color = '#1890ff';
              }
            }}
            onMouseLeave={(e) => {
              if (!e.currentTarget.disabled) {
                e.currentTarget.style.borderColor = '#d9d9d9';
                e.currentTarget.style.color = '#666';
              }
            }}
          >
            格式化
          </button>
          <button
            style={buttonStyle}
            onClick={() => {
              setText('');
              setError(null);
              onChange(undefined);
            }}
            disabled={readOnly || text.trim() === ''}
            onMouseEnter={(e) => {
              if (!e.currentTarget.disabled) {
                e.currentTarget.style.borderColor = '#ff4d4f';
                e.currentTarget.style.color = '#ff4d4f';
              }
            }}
            onMouseLeave={(e) => {
              if (!e.currentTarget.disabled) {
                e.currentTarget.style.borderColor = '#d9d9d9';
                e.currentTarget.style.color = '#666';
              }
            }}
          >
            清空
          </button>
        </div>
      </div>
    </div>
  );
};
