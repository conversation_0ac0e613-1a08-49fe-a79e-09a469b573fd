import React, { useEffect, useState, useCallback, useRef } from 'react';
import { message, Alert, Spin } from 'antd';
import { createRenderer } from '@lowcode/renderer';
import type { PageSchema } from '@lowcode/shared';
import {
  SchemaLoader
} from '@lowcode/shared';




const App: React.FC = () => {
  const [schema, setSchema] = useState<PageSchema | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const hasLoadedRef = useRef(false);

  // 加载Schema - 直接从服务器加载
  const loadSchema = useCallback(async () => {
    setLoading(true);
    setError(null);

    try {
      // 直接从服务器加载 api-management.json
      const serverSchema = await SchemaLoader.loadFromServer('api-management.json');
      setSchema(serverSchema);
      message.success('已从服务器加载Schema');
    } catch (serverError) {
      console.error('Failed to load schema from server:', serverError);
      setError('从服务器加载Schema失败，请检查服务器连接');
      message.error('服务器连接失败，无法加载页面');
    } finally {
      setLoading(false);
    }
  }, []);





  // 初始加载
  useEffect(() => {
    // 防止 React.StrictMode 导致的重复执行
    if (hasLoadedRef.current) return;
    hasLoadedRef.current = true;

    loadSchema();
  }, [loadSchema]);

  // 渲染Schema
  const renderSchema = () => {
    if (!schema) {
      return (
        <div style={{
          height: '100%',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          flexDirection: 'column',
          gap: '16px'
        }}>
          <Alert
            message="无法加载页面"
            description="Schema 加载失败，请检查服务器连接或联系管理员"
            type="error"
            showIcon
          />
        </div>
      );
    }

    try {
      const renderer = createRenderer();
      return renderer.renderPage(schema);
    } catch (err) {
      console.error('Render error:', err);
      return (
        <Alert
          message="渲染错误"
          description={`Schema渲染失败: ${err instanceof Error ? err.message : '未知错误'}`}
          type="error"
          showIcon
        />
      );
    }
  };

  if (loading) {
    return (
      <div style={{
        height: '100vh',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center'
      }}>
        <Spin size="large" tip="加载中...">
          <div style={{ minHeight: '200px' }} />
        </Spin>
      </div>
    );
  }

  return (
    <div style={{ height: '100vh', display: 'flex', flexDirection: 'column' }}>
      {/* 错误提示 - 如果有错误，显示在页面顶部 */}
      {error && (
        <Alert
          message={error}
          type="error"
          closable
          onClose={() => setError(null)}
          style={{
            flexShrink: 0,
            zIndex: 2000,
            margin: 0,
            borderRadius: 0
          }}
        />
      )}

      {/* 主要渲染区域 - 占满剩余空间 */}
      <div style={{ flex: 1, overflow: 'hidden' }}>
        {renderSchema()}
      </div>
    </div>
  );
};

export default App;
