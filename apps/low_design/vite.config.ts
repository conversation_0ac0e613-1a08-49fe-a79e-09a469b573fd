import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'
import { resolve } from 'path'

export default defineConfig({
  plugins: [react()],
  resolve: {
    alias: {
      '@': resolve(__dirname, 'src'),
      '@lowcode/shared': resolve(__dirname, '../../packages/shared/src'),
      '@lowcode/designer': resolve(__dirname, '../../packages/designer/src')
    }
  },
  define: {
    // 设置设计器环境标识
    'process.env.NODE_ENV': '"development"',
    'process.env.REACT_APP_ENV': '"designer"'
  },
  server: {
    port: 3001,
    host: true,
    fs: {
      // 允许访问项目根目录外的文件
      allow: ['..', '../..']
    }
  },
  build: {
    outDir: 'dist',
    sourcemap: true
  },
  // 配置静态资源
  publicDir: resolve(__dirname, '../../examples')
})
