{"name": "@lowcode/server", "version": "1.0.0", "description": "低代码平台服务器端", "main": "dist/index.js", "scripts": {"dev": "tsx watch src/index.ts", "build": "tsc", "start": "node dist/index.js", "clean": "rm -rf dist"}, "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "multer": "^1.4.5-lts.1", "fs-extra": "^11.1.1", "path": "^0.12.7"}, "devDependencies": {"@types/express": "^4.17.17", "@types/cors": "^2.8.13", "@types/multer": "^1.4.7", "@types/fs-extra": "^11.0.1", "@types/node": "^20.5.0", "tsx": "^3.12.7", "typescript": "^5.1.6"}, "keywords": ["lowcode", "server", "api"], "author": "", "license": "MIT"}