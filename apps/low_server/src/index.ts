import express from 'express';
import cors from 'cors';
import path from 'path';
import fs from 'fs-extra';
import { schemaRoutes } from './routes/schema';

const app = express();
const PORT = process.env.PORT || 3005;

// 中间件
app.use(cors());
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true }));

// 静态文件服务 - 提供 examples 目录访问
const examplesPath = path.resolve(__dirname, '../../../examples');
app.use('/examples', express.static(examplesPath));

// API 路由
app.use('/api/schemas', schemaRoutes);

// 健康检查
app.get('/health', (req, res) => {
  res.json({ 
    status: 'ok', 
    timestamp: new Date().toISOString(),
    service: 'lowcode-server'
  });
});

// 根路径
app.get('/', (req, res) => {
  res.json({
    message: '低代码平台服务器',
    version: '1.0.0',
    endpoints: {
      schemas: '/api/schemas',
      health: '/health',
      examples: '/examples'
    }
  });
});

// 错误处理中间件
app.use((err: any, req: express.Request, res: express.Response, next: express.NextFunction) => {
  console.error('Error:', err);
  res.status(500).json({
    error: 'Internal Server Error',
    message: err.message
  });
});

// 404 处理
app.use((req, res) => {
  res.status(404).json({
    error: 'Not Found',
    message: `Route ${req.method} ${req.path} not found`
  });
});

// 启动服务器
app.listen(PORT, () => {
  console.log(`🚀 低代码平台服务器启动成功`);
  console.log(`📍 服务地址: http://localhost:${PORT}`);
  console.log(`📁 静态文件: http://localhost:${PORT}/examples`);
  console.log(`🔗 API文档: http://localhost:${PORT}/api/schemas`);
  console.log(`❤️  健康检查: http://localhost:${PORT}/health`);
});

export default app;
