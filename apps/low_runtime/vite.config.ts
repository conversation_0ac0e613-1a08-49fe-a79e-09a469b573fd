import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'
import { resolve } from 'path'

export default defineConfig({
  plugins: [react()],
  resolve: {
    alias: {
      '@': resolve(__dirname, 'src'),
      '@lowcode/shared': resolve(__dirname, '../../packages/shared/src'),
      '@lowcode/renderer': resolve(__dirname, '../../packages/renderer/src')
    }
  },
  define: {
    // 设置运行时环境标识
    'process.env.REACT_APP_ENV': '"runtime"'
  },
  server: {
    port: 3003,
    host: true,
    fs: {
      // 允许访问项目根目录外的文件
      allow: ['..', '../..']
    }
  },
  build: {
    outDir: 'dist',
    sourcemap: true
  },
  // 配置静态资源
  publicDir: resolve(__dirname, '../../examples')
})
