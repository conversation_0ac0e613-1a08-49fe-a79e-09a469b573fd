/* 分发构建时由示例应用静态资源引用 */
/* 内容来源 packages/renderer/src/theme/design.css */
:root { --lc-brand: #1677ff; --lc-bg: #ffffff; --lc-text: #111827; --lc-border: #e5e7eb; }
.lc-component-library{background-color:var(--lc-bg);border-right:1px solid var(--lc-border);display:flex;flex-direction:column;position:relative;overflow:hidden}
.lc-section-header{width:100%;display:flex;align-items:center;justify-content:space-between;padding:4px 8px;cursor:pointer;background:transparent;border:none}
.lc-section-title{font-size:12px;font-weight:600;color:#374151}
.lc-icon-transition{transition:transform .2s ease;color:#6b7280}
.lc-collapse{transition:transform .1s ease,opacity .1s ease;transform-origin:top}
.lc-open{transform:scale(1);opacity:1}
.lc-closed{transform:scale(.98);opacity:0;height:0;overflow:hidden}
.lc-toggle-button{position:absolute;right:-12px;top:50%;transform:translateY(-50%);z-index:10;background-color:var(--lc-bg);border:1px solid var(--lc-border);border-radius:9999px;padding:4px;cursor:pointer;box-shadow:0 1px 2px rgba(0,0,0,0.04)}
.lc-toggle-button:focus{outline:none;box-shadow:0 0 0 3px rgba(59,130,246,0.15)}
.lc-sidebar-nav{width:80px;border-right:1px solid var(--lc-border)}
.lc-nav-inner{display:flex;flex-direction:column;gap:4px;padding:8px}
.lc-tab{display:flex;flex-direction:column;align-items:center;padding:8px;border-left:4px solid transparent;border-radius:6px;cursor:pointer;transition:color .2s,background-color .2s}
.lc-tab-active{background-color:#eff6ff;color:#2563eb;border-left-color:#3b82f6}
.lc-tab-inactive{color:#6b7280}
.lc-tab-inactive:hover{background-color:#f3f4f6;color:#374151}
.lc-panel{flex:1;overflow-y:auto;padding:12px;background:#fff}
.lc-grid{display:grid;grid-template-columns:1fr;gap:8px}
.lc-card{padding:8px;border:1px solid var(--lc-border);border-radius:8px;text-align:center;cursor:move;transition:all .2s;background-color:var(--lc-bg)}
.lc-card:hover{background-color:#f9fafb;border-color:#3b82f6}
.lc-card-icon{color:#6b7280;font-size:24px}
.lc-card-title{font-size:12px;color:#1f2937;margin-top:4px;font-weight:500}
.lc-content{display:flex;flex:1;transition:opacity .3s ease}
body{font-family:Inter,system-ui,-apple-system,Segoe UI,Roboto,Helvetica,Arial,"Apple Color Emoji","Segoe UI Emoji","Segoe UI Symbol",sans-serif;margin:0;padding:0;background-color:#f3f4f6}

