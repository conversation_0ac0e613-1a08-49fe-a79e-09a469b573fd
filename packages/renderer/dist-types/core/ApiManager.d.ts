import { ApiConfig } from '../types';
export declare class ApiManager {
    private apis;
    private cache;
    private baseURL;
    private requestInterceptors;
    private responseInterceptors;
    constructor(baseURL?: string);
    registerApi(config: ApiConfig): void;
    getApiConfig(id: string): ApiConfig | undefined;
    callApi(id: string, params?: Record<string, any>): Promise<any>;
    callApiWithCache(id: string, params?: Record<string, any>, cacheKey?: string): Promise<any>;
    private buildUrl;
    private buildRequestOptions;
    private extractDataByPath;
    clearCache(key?: string): void;
    setBaseURL(url: string): void;
    getAllApis(): ApiConfig[];
    removeApi(id: string): void;
    clear(): void;
    addRequestInterceptor(interceptor: (config: RequestInit) => RequestInit): void;
    addResponseInterceptor(interceptor: (response: any) => any): void;
    removeRequestInterceptor(interceptor: (config: RequestInit) => RequestInit): void;
    removeResponseInterceptor(interceptor: (response: any) => any): void;
    callMultipleApis(calls: Array<{
        id: string;
        params?: Record<string, any>;
    }>): Promise<any[]>;
    callMultipleApisAllSettled(calls: Array<{
        id: string;
        params?: Record<string, any>;
    }>): Promise<PromiseSettledResult<any>[]>;
    retryApi(id: string, params?: Record<string, any>, maxRetries?: number, delay?: number): Promise<any>;
}
//# sourceMappingURL=ApiManager.d.ts.map