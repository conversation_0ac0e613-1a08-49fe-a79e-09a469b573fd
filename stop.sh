#!/bin/bash

# 低代码平台停止脚本
# 该脚本会停止所有正在运行的应用服务

set -e  # 遇到错误时退出

echo "🛑 开始停止低代码平台..."
echo "================================"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 停止进程函数
stop_process() {
    local service_name=$1
    local pid_file=$2
    
    if [ -f "$pid_file" ]; then
        local pid=$(cat "$pid_file")
        if ps -p $pid > /dev/null 2>&1; then
            log_info "停止 $service_name (PID: $pid)..."
            kill $pid
            
            # 等待进程结束
            local count=0
            while ps -p $pid > /dev/null 2>&1 && [ $count -lt 10 ]; do
                sleep 1
                count=$((count + 1))
            done
            
            # 如果进程仍在运行，强制杀死
            if ps -p $pid > /dev/null 2>&1; then
                log_warning "$service_name 未正常停止，强制终止..."
                kill -9 $pid
            fi
            
            log_success "$service_name 已停止"
        else
            log_warning "$service_name 进程不存在 (PID: $pid)"
        fi
        rm -f "$pid_file"
    else
        log_warning "$service_name PID 文件不存在"
    fi
}

# 停止所有基于端口的进程
stop_by_port() {
    local port=$1
    local service_name=$2
    
    log_info "检查端口 $port 上的 $service_name 进程..."
    
    # 查找占用端口的进程
    local pids=$(lsof -ti:$port 2>/dev/null || true)
    
    if [ -n "$pids" ]; then
        for pid in $pids; do
            if ps -p $pid > /dev/null 2>&1; then
                log_info "停止端口 $port 上的进程 (PID: $pid)..."
                kill $pid 2>/dev/null || true
                
                # 等待进程结束
                local count=0
                while ps -p $pid > /dev/null 2>&1 && [ $count -lt 5 ]; do
                    sleep 1
                    count=$((count + 1))
                done
                
                # 如果进程仍在运行，强制杀死
                if ps -p $pid > /dev/null 2>&1; then
                    kill -9 $pid 2>/dev/null || true
                fi
            fi
        done
        log_success "端口 $port 上的 $service_name 进程已停止"
    else
        log_info "端口 $port 上没有运行的进程"
    fi
}

# 清理函数
cleanup() {
    log_info "清理临时文件..."
    
    # 清理 PID 文件
    rm -rf .pids
    
    # 清理 node_modules/.vite 缓存（可选）
    # rm -rf apps/*/node_modules/.vite
    # rm -rf packages/*/node_modules/.vite
    
    log_success "清理完成"
}

# 显示停止信息
show_stop_info() {
    echo ""
    echo "================================"
    log_success "🎉 低代码平台已完全停止！"
    echo "================================"
    echo ""
    echo "📋 可用命令："
    echo "   重新启动:    ./start.sh"
    echo "   查看日志:    tail -f logs/*.log"
    echo ""
}

# 主函数
main() {
    # 方法1: 通过 PID 文件停止
    log_info "通过 PID 文件停止服务..."
    stop_process "服务器" ".pids/server.pid"
    stop_process "设计器应用" ".pids/design.pid"
    stop_process "运行时应用" ".pids/runtime.pid"
    
    # 方法2: 通过端口停止（备用方案）
    log_info "检查并停止端口占用进程..."
    stop_by_port "3005" "服务器"
    stop_by_port "3001" "设计器应用"
    stop_by_port "3003" "运行时应用"
    stop_by_port "3004" "运行时应用(备用端口)"
    stop_by_port "3006" "设计器应用(备用端口)"
    stop_by_port "3007" "运行时应用(备用端口)"
    
    # 额外检查常见的开发服务器进程
    log_info "检查其他相关进程..."
    
    # 查找并停止 vite 进程
    local vite_pids=$(pgrep -f "vite.*--port" 2>/dev/null || true)
    if [ -n "$vite_pids" ]; then
        for pid in $vite_pids; do
            if ps -p $pid > /dev/null 2>&1; then
                log_info "停止 Vite 开发服务器 (PID: $pid)..."
                kill $pid 2>/dev/null || true
            fi
        done
    fi
    
    # 查找并停止 tsx 进程
    local tsx_pids=$(pgrep -f "tsx.*watch" 2>/dev/null || true)
    if [ -n "$tsx_pids" ]; then
        for pid in $tsx_pids; do
            if ps -p $pid > /dev/null 2>&1; then
                log_info "停止 TSX 开发服务器 (PID: $pid)..."
                kill $pid 2>/dev/null || true
            fi
        done
    fi
    
    # 等待所有进程完全停止
    sleep 2
    
    cleanup
    show_stop_info
}

# 捕获中断信号
trap 'log_warning "停止过程被中断"; exit 1' INT

# 检查是否有 lsof 命令
if ! command -v lsof &> /dev/null; then
    log_warning "lsof 命令未找到，将跳过端口检查"
    log_info "建议安装 lsof: brew install lsof (macOS) 或 apt-get install lsof (Ubuntu)"
fi

# 执行主函数
main
