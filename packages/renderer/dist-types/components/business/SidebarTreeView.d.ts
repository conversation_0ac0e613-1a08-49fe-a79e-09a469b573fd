import React from 'react';
export interface TreeNode {
    key: string;
    title: string;
    icon?: string;
    href?: string;
    children?: TreeNode[];
    disabled?: boolean;
}
export interface SidebarTreeViewProps {
    data: TreeNode[];
    width?: number | string;
    height?: number | string;
    searchable?: boolean;
    searchPlaceholder?: string;
    defaultExpandedKeys?: string[];
    defaultSelectedKeys?: string[];
    showIcon?: boolean;
    style?: React.CSSProperties;
    className?: string;
    onSelect?: (selectedKeys: string[], node: TreeNode) => void;
    onExpand?: (expandedKeys: string[], node: TreeNode) => void;
    onSearch?: (value: string) => void;
}
export declare const SidebarTreeView: React.FC<SidebarTreeViewProps>;
//# sourceMappingURL=SidebarTreeView.d.ts.map