import React, { createContext, useContext, ReactNode } from 'react';
import type { ComponentSchema } from '../types';
import { applyDataMappingToArray, applyDataMappingToTree, type DataMapping } from '../utils/dataMapping';

// 组件数据上下文接口
interface ComponentDataContextType {
  getComponentData: (componentId: string) => ComponentSchema | undefined;
  setComponentData: (componentId: string, data: ComponentSchema) => void;
}

// 创建上下文
const ComponentDataContext = createContext<ComponentDataContextType | undefined>(undefined);

// Provider组件的Props
interface ComponentDataProviderProps {
  children: ReactNode;
  components?: Record<string, ComponentSchema>;
}

// Provider组件
export const ComponentDataProvider: React.FC<ComponentDataProviderProps> = ({
  children,
  components = {}
}) => {
  const [componentData, setComponentData] = React.useState<Record<string, ComponentSchema>>(components);

  // 当components prop更新时，同步更新内部状态
  React.useEffect(() => {
    setComponentData(components);
  }, [components]);

  const getComponentData = (componentId: string): ComponentSchema | undefined => {
    return componentData[componentId];
  };

  const setComponentDataItem = (componentId: string, data: ComponentSchema) => {
    setComponentData(prev => ({
      ...prev,
      [componentId]: data
    }));
  };

  const contextValue: ComponentDataContextType = {
    getComponentData,
    setComponentData: setComponentDataItem
  };

  return (
    <ComponentDataContext.Provider value={contextValue}>
      {children}
    </ComponentDataContext.Provider>
  );
};

// Hook来使用上下文
export const useComponentData = (): ComponentDataContextType => {
  const context = useContext(ComponentDataContext);
  if (!context) {
    throw new Error('useComponentData must be used within a ComponentDataProvider');
  }
  return context;
};

// Hook来获取特定组件的mockData（支持新旧格式兼容）
export const useMockData = (componentId?: string, eventId: string = 'onMount'): any[] => {
  const { getComponentData } = useComponentData();

  return React.useMemo(() => {
    if (!componentId) {
      return [];
    }

    const componentData = getComponentData(componentId);

    if (!componentData) {
      return [];
    }

    // 优先从事件配置中获取模拟数据（新格式）
    if (componentData.events) {
      const eventConfig = componentData.events[eventId];
      if (eventConfig && eventConfig.type === 'callApi') {
        const apiConfig = eventConfig.config?.apiConfig;
        if (apiConfig && apiConfig.mockData) {
          return Array.isArray(apiConfig.mockData) ? apiConfig.mockData : [apiConfig.mockData];
        }
      }
    }

    // 向后兼容：从组件的mockData字段获取（旧格式）
    if (componentData.mockData) {
      try {
        const mockData = JSON.parse(componentData.mockData);
        return Array.isArray(mockData) ? mockData : [mockData];
      } catch (error) {
        console.warn('Failed to parse mockData for component:', componentId, error);
        return [];
      }
    }

    return [];
  }, [componentId, eventId, getComponentData]);
};

// Hook来获取应用了数据映射的组件数据
export const useMappedData = (
  componentId?: string,
  eventId: string = 'onMount',
  isTreeData: boolean = false
): any[] => {
  const { getComponentData } = useComponentData();
  const [apiData, setApiData] = React.useState<any[]>([]);
  const [isLoading, setIsLoading] = React.useState(false);
  const [hasTriedApi, setHasTriedApi] = React.useState(false);

  // 获取组件配置和映射
  const { rawData, dataMapping, apiConfig } = React.useMemo(() => {
    if (!componentId) {
      return { rawData: [], dataMapping: {}, apiConfig: null };
    }

    const componentData = getComponentData(componentId);
    if (!componentData) {
      return { rawData: [], dataMapping: {}, apiConfig: null };
    }

    let rawData: any[] = [];
    let dataMapping: DataMapping = {};
    let apiConfig: any = null;

    // 获取原始数据和映射配置
    if (componentData.events) {
      const eventConfig = componentData.events[eventId];
      if (eventConfig && eventConfig.type === 'callApi') {
        apiConfig = eventConfig.config?.apiConfig;
        if (apiConfig) {
          // 获取模拟数据
          if (apiConfig.mockData) {
            rawData = Array.isArray(apiConfig.mockData) ? apiConfig.mockData : [apiConfig.mockData];
          }
          // 获取数据映射配置
          if (apiConfig.dataMapping) {
            dataMapping = apiConfig.dataMapping;
          }
        }
      }
    } else if (componentData.mockData) {
      // 向后兼容：从组件的mockData字段获取（旧格式）
      try {
        const mockData = JSON.parse(componentData.mockData);
        rawData = Array.isArray(mockData) ? mockData : [mockData];
      } catch (error) {
        console.warn('Failed to parse mockData for component:', componentId, error);
        return { rawData: [], dataMapping: {}, apiConfig: null };
      }
    }

    return { rawData, dataMapping, apiConfig };
  }, [componentId, eventId, getComponentData]);

  // 尝试调用真实API（仅在运行时环境）
  React.useEffect(() => {
    if (!apiConfig || hasTriedApi || isLoading) {
      return;
    }

    // 检查是否在运行时环境（通过环境变量判断）
    const isRuntime = (typeof process !== 'undefined' &&
                      (process.env.NODE_ENV === 'production' ||
                       process.env.REACT_APP_ENV === 'runtime')) ||
                     window.location.pathname.includes('/runtime') ||
                     document.title.includes('Runtime') ||
                     // 检查是否存在运行时特有的标识
                     (window as any).__LOWCODE_RUNTIME__ === true;

    if (!isRuntime) {
      // 设计器环境直接使用模拟数据
      setHasTriedApi(true);
      return;
    }

    const callRealApi = async () => {
      setIsLoading(true);
      try {
        const { url, method = 'GET', headers = {} } = apiConfig;

        // 检查URL是否为绝对路径，如果不是则跳过API调用
        if (!url.startsWith('http://') && !url.startsWith('https://')) {
          console.warn(`[Runtime API] 相对路径API调用跳过: ${url}`);
          throw new Error(`相对路径API调用跳过: ${url}`);
        }

        const response = await fetch(url, {
          method,
          headers: {
            'Content-Type': 'application/json',
            ...headers
          },
          signal: AbortSignal.timeout(5000) // 5秒超时
        });

        if (response.ok) {
          const contentType = response.headers.get('content-type');
          if (!contentType || !contentType.includes('application/json')) {
            throw new Error(`响应不是JSON格式: ${contentType}`);
          }

          const result = await response.json();
          let data = result;

          // 统一从响应的data字段提取数据
          if (data && typeof data === 'object' && 'data' in data) {
            data = data.data;
          }

          const dataArray = Array.isArray(data) ? data : [data];
          setApiData(dataArray);
          console.log(`[Runtime API] 成功获取数据:`, dataArray);
        } else {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
      } catch (error) {
        console.warn(`[Runtime API] API调用失败，组件将显示为空:`, error);
        // API调用失败时不设置apiData，组件将显示为空
      } finally {
        setIsLoading(false);
        setHasTriedApi(true);
      }
    };

    callRealApi();
  }, [apiConfig, hasTriedApi, isLoading]);

  // 返回最终数据
  return React.useMemo(() => {
    // 如果没有数据映射配置，返回空数组（不自动映射）
    if (!dataMapping || Object.keys(dataMapping).length === 0) {
      return [];
    }

    // 检查是否在运行时环境
    const isRuntime = (typeof process !== 'undefined' &&
                      (process.env.NODE_ENV === 'production' ||
                       process.env.REACT_APP_ENV === 'runtime')) ||
                     window.location.pathname.includes('/runtime') ||
                     document.title.includes('Runtime') ||
                     (window as any).__LOWCODE_RUNTIME__ === true;

    let sourceData: any[] = [];

    if (isRuntime) {
      // 运行时环境：只使用API数据，API失败时显示空
      if (hasTriedApi && apiData.length > 0) {
        sourceData = apiData;
      } else {
        // API调用失败或还未尝试，显示空数据
        return [];
      }
    } else {
      // 设计器环境：优先使用API数据，如果没有则使用模拟数据
      sourceData = apiData.length > 0 ? apiData : rawData;
    }

    // 应用数据映射
    if (isTreeData) {
      return applyDataMappingToTree(sourceData, dataMapping);
    } else {
      return applyDataMappingToArray(sourceData, dataMapping);
    }
  }, [apiData, rawData, dataMapping, isTreeData, hasTriedApi]);
};
