{"name": "@lowcode/renderer", "version": "1.0.0", "description": "Low-code platform renderer engine", "main": "src/index.ts", "module": "src/index.ts", "types": "src/index.ts", "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview", "test": "vitest", "lint": "eslint src --ext .ts,.tsx", "clean": "<PERSON><PERSON><PERSON> dist"}, "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "@lowcode/shared": "file:../shared"}, "devDependencies": {"@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "@types/lodash": "^4.14.0", "@vitejs/plugin-react": "^4.0.0", "typescript": "^5.0.0", "vite": "^4.0.0", "vitest": "^0.34.0", "eslint": "^8.0.0", "rimraf": "^5.0.0"}, "peerDependencies": {"react": ">=18.0.0", "react-dom": ">=18.0.0"}}