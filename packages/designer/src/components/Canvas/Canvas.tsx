import React, { useRef } from 'react';
import { createRenderer } from '@lowcode/renderer';
import { useDesigner } from '../../context/DesignerContext';

export interface CanvasProps {
  style?: React.CSSProperties;
  className?: string;
}

const CanvasComponent: React.FC<CanvasProps> = ({ style, className }) => {
  const { schema, canvasState, selectComponent, hoverComponent } = useDesigner();
  const canvasRef = useRef<HTMLDivElement>(null);
  const rendererRef = useRef(createRenderer());

  // 处理组件点击
  const handleComponentClick = (event: React.MouseEvent) => {
    // 不阻止事件传播，让内部组件的事件正常工作
    const target = event.target as HTMLElement;
    const componentElement = target.closest('[data-component-id]');

    if (componentElement) {
      const componentId = componentElement.getAttribute('data-component-id');
      selectComponent(componentId || undefined);
    } else {
      selectComponent(undefined);
    }
  };

  // 处理组件悬停
  const handleComponentMouseEnter = (event: React.MouseEvent) => {
    const target = event.target as HTMLElement;
    const componentElement = target.closest('[data-component-id]');

    if (componentElement) {
      const componentId = componentElement.getAttribute('data-component-id');
      hoverComponent(componentId || undefined);
    }
  };

  // 处理组件离开悬停
  const handleComponentMouseLeave = () => {
    hoverComponent(undefined);
  };

  // 渲染页面内容
  const renderPageContent = () => {
    if (canvasState.mode === 'preview') {
      // 预览模式：直接渲染
      return rendererRef.current.renderPage(schema);
    } else {
      // 设计模式：直接渲染，不使用cloneElement避免干扰事件
      return rendererRef.current.renderPage(schema);
    }
  };

  // 渲染选中状态指示器
  const renderSelectionIndicator = () => {
    if (!canvasState.selectedComponentId || canvasState.mode === 'preview') {
      return null;
    }

    return (
      <div
        style={{
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          border: '2px solid #1890ff',
          borderRadius: '4px',
          pointerEvents: 'none',
          zIndex: 1000
        }}
      />
    );
  };

  // 渲染悬停状态指示器
  const renderHoverIndicator = () => {
    // 完全禁用悬停指示器
    return null;
  };

  const defaultStyle: React.CSSProperties = {
    width: '100%',
    height: '100%',
    backgroundColor: '#ffffff',
    overflow: 'auto',
    position: 'relative',
    transform: `scale(${canvasState.scale})`,
    transformOrigin: 'top left',
    ...style,
  };

  return (
    <div
      ref={canvasRef}
      className={`lowcode-canvas ${className || ''}`}
      style={defaultStyle}
      onClick={handleComponentClick}
      onMouseEnter={handleComponentMouseEnter}
      onMouseLeave={handleComponentMouseLeave}
    >
      {/* 页面内容 */}
      <div style={{ position: 'relative' }}>
        {renderPageContent()}
        {renderSelectionIndicator()}
        {renderHoverIndicator()}
      </div>

      {/* 空状态提示 */}
      {schema.components.length === 0 && (
        <div
          style={{
            position: 'absolute',
            top: '50%',
            left: '50%',
            transform: 'translate(-50%, -50%)',
            textAlign: 'center',
            color: '#999',
            fontSize: '16px'
          }}
        >
          <div style={{ fontSize: '48px', marginBottom: '16px' }}>📦</div>
          <div>拖拽组件到这里开始设计</div>
        </div>
      )}
    </div>
  );
};

export const Canvas = React.memo(CanvasComponent);
