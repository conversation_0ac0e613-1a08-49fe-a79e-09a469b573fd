/* 设计稿侧栏样式还原（尽量贴近 Tailwind 视觉） */
:root {
  --lc-brand: var(--lowcode-primary-color, #1677ff);
  --lc-brand-600: #2563eb; /* blue-600 */
  --lc-brand-700: #1d4ed8; /* blue-700 */
  --lc-bg: var(--lowcode-background-color, #ffffff);
  --lc-text: var(--lowcode-text-color, #111827);
  --lc-border: var(--lowcode-border-color, #e5e7eb);
}

/* 页面背景与字体 */
body {
  background-color: #f3f4f6;
  font-family: Inter, system-ui, -apple-system, Segoe UI, Roboto, Helvetica, Arial, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", sans-serif;
  margin: 0;
  padding: 0;
}

/* 容器与边框 */
.lc-component-library { background-color: var(--lc-bg); border-right: 1px solid var(--lc-border); display:flex; flex-direction:column; position:relative; overflow:hidden; }

/* 区段标题（基础组件） */
.lc-section-header { width:100%; display:flex; align-items:center; justify-content:space-between; padding:4px 8px; cursor:pointer; background:transparent; border:none; }
.lc-section-title { font-size:12px; font-weight:600; color:#374151; }
.lc-icon-transition { transition: transform .2s ease; color:#6b7280; }

/* 折叠过渡 */
.lc-collapse { transition: transform .1s ease, opacity .1s ease; transform-origin: top; }
.lc-open { transform: scale(1); opacity:1; }
.lc-closed { transform: scale(0.98); opacity:0; height:0; overflow:hidden; }

/* 折叠按钮 */
.lc-toggle-button {
  position: absolute;
  right: -12px;
  top: 50%;
  transform: translateY(-50%);
  z-index: 10;
  background-color: var(--lc-bg);
  border: 1px solid var(--lc-border);
  border-radius: 9999px;
  padding: 4px;
  cursor: pointer;
  box-shadow: 0 1px 2px rgba(0,0,0,0.04);
}
.lc-toggle-button:focus { outline: none; box-shadow: 0 0 0 3px rgba(59,130,246,0.15); }

/* 左侧竖向标签栏 */
.lc-sidebar-nav { width: 80px; border-right: 1px solid var(--lc-border); background: #ffffff; }
.lc-nav-inner { display: flex; flex-direction: column; gap: 4px; padding: 8px; background:#fff; }

/* Tab 基础、激活、未激活与悬浮 */
.lc-tab {
  display: flex; flex-direction: column; align-items: center;
  padding: 8px; border-left: 4px solid transparent; border-radius: 6px;
  cursor: pointer; transition: color .2s, background-color .2s;
}
.lc-tab-active { background-color: #eff6ff; color: #2563eb; border-left-color: #3b82f6; }
.lc-tab-inactive { color: #6b7280; }
.lc-tab-inactive:hover { background-color: #f3f4f6; color: #374151; }

/* 右侧面板与卡片 */
.lc-panel { flex: 1; overflow-y: auto; padding: 12px; }
.lc-grid { display: grid; grid-template-columns: 1fr; gap: 8px; }
.lc-card { padding: 8px; border: 1px solid var(--lc-border); border-radius: 8px; text-align: center; cursor: move; transition: all .2s; background-color: var(--lc-bg); }
.lc-card:hover { background-color: #f9fafb; border-color: #3b82f6; }
.lc-card-icon { color: #6b7280; font-size: 24px; }
.lc-card-title { font-size: 12px; color: #1f2937; margin-top: 4px; font-weight: 500; }

/* 折叠时内容透明（与设计稿淡入淡出一致） */
.lc-content { display: flex; flex: 1; transition: opacity .3s ease; }

/* 通用按钮与输入（用于顶部按钮、表单等） */
.lc-btn { border: none; cursor: pointer; font-size: 14px; line-height: 20px; border-radius: 6px; padding: 8px 16px; transition: background-color .2s, color .2s, border-color .2s; }
.lc-btn-primary { background-color: var(--lc-brand-600); color: #fff; font-weight: 600; }
.lc-btn-primary:hover { background-color: var(--lc-brand-700); }
.lc-btn-default { background-color: #ffffff; color: #4b5563; font-weight: 500; border: 1px solid var(--lc-border); }
.lc-btn-default:hover { background-color: #f3f4f6; color: #374151; }

.lc-input { width: 100%; padding: 8px 12px; border: 1px solid var(--lc-border); border-radius: 6px; font-size: 14px; outline: none; }
.lc-input:focus { border-color: #93c5fd; box-shadow: 0 0 0 3px rgba(59,130,246,0.15); }

/* 页面卡片容器（大块白底区域） */
.lc-page-card { background: #fff; border: 1px solid var(--lc-border); border-radius: 12px; padding: 24px; box-shadow: 0 1px 2px rgba(0,0,0,0.04); }

