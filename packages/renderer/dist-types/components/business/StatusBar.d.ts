import React from 'react';
export interface StatusItem {
    key: string;
    label?: string;
    value: string | number;
    icon?: string;
    color?: string;
    clickable?: boolean;
    onClick?: () => void;
}
export interface StatusBarProps {
    items: StatusItem[];
    position?: 'fixed' | 'relative';
    height?: number;
    backgroundColor?: string;
    textColor?: string;
    borderTop?: string;
    showSeparator?: boolean;
    style?: React.CSSProperties;
    className?: string;
    onItemClick?: (item: StatusItem) => void;
}
export declare const StatusBar: React.FC<StatusBarProps>;
//# sourceMappingURL=StatusBar.d.ts.map