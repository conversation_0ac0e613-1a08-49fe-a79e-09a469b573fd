import { ComponentSchema, ComponentMeta } from '../types';
export declare function generateId(prefix?: string): string;
export declare function deepClone<T>(obj: T): T;
export declare function findComponent(schema: ComponentSchema[], id: string): ComponentSchema | null;
export declare function findParentComponent(schema: ComponentSchema[], childId: string): ComponentSchema | null;
export declare function removeComponent(schema: ComponentSchema[], id: string): ComponentSchema[];
export declare function updateComponent(schema: ComponentSchema[], id: string, updates: Partial<ComponentSchema>): ComponentSchema[];
export declare function validateComponentSchema(schema: ComponentSchema): string[];
export declare function createDefaultComponentSchema(type: string, meta?: ComponentMeta): ComponentSchema;
export declare function flattenComponentTree(schema: ComponentSchema[]): ComponentSchema[];
export declare function getComponentPath(schema: ComponentSchema[], id: string): number[];
//# sourceMappingURL=index.d.ts.map