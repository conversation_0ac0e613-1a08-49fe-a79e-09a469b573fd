import React from 'react';
import { useMappedData } from '../../context/ComponentDataContext';
import { useEventHandler } from '../../hooks/useEventHandler';

export interface TableColumn {
  key: string;
  title: string;
  dataIndex: string;
  width?: number | string;
  align?: 'left' | 'center' | 'right';
  sortable?: boolean;
  filterable?: boolean;
  render?: (value: any, record: any, index: number) => React.ReactNode;
}

export interface TableAction {
  key: string;
  label: string;
  icon?: string;
  type?: 'primary' | 'default' | 'danger';
  disabled?: boolean;
  onClick?: (record: any, index: number) => void;
}

export interface ToolbarAction {
  key: string;
  label: string;
  icon?: string;
  type?: 'primary' | 'default' | 'danger';
  disabled?: boolean;
  onClick?: () => void;
}

export interface SearchField {
  key: string;
  label: string;
  type: 'input' | 'select' | 'date' | 'dateRange' | 'combinedSelect' | 'checkbox';
  placeholder?: string;
  options?: Array<{ label: string; value: any }>;
  // 组合选择器配置
  combinedConfig?: {
    leftOptions?: Array<{ label: string; value: any }>;
    rightOptions?: Array<{ label: string; value: any }>;
  };
  // 复选框配置
  checkboxConfig?: {
    text: string;
    tooltip?: string;
  };
}

export interface GroupTab {
  key: string;
  label: string;
  count?: number;
  active?: boolean;
}

// 组件可映射的字段定义 - 表格行数据字段
export const TableViewWithSearchMappableFields = {
  id: { label: 'ID', type: 'string|number', required: true, description: '唯一标识符' },
  path: { label: '路径', type: 'string', required: false, description: 'API路径' },
  sensitivity: { label: '敏感度', type: 'string', required: false, description: '敏感度级别' },
  riskLevel: { label: '风险等级', type: 'string', required: false, description: '风险等级' },
  totalVisits: { label: '访问次数', type: 'string|number', required: false, description: '总访问次数' },
  trafficSource: { label: '流量来源', type: 'string', required: false, description: '流量来源IP' },
  firstSeen: { label: '首次发现', type: 'string', required: false, description: '首次发现时间' }
} as const;

export interface TableViewWithSearchProps extends React.HTMLAttributes<HTMLDivElement> {
  // 基本配置
  title?: string;
  columns: TableColumn[];
  loading?: boolean;
  // 组件ID，用于获取模拟数据
  componentId?: string;
  // 事件配置
  events?: Record<string, any>;

  // 自定义事件处理器（不会传递到DOM）
  onMount?: (data?: any) => void;
  onRowClick?: (data?: any) => void;
  onPageChange?: (data?: any) => void;
  onToolbarAction?: (data?: any) => void;
  onNodeClick?: (data?: any) => void;
  onNodeExpand?: (data?: any) => void;
  onMenuClick?: (data?: any) => void;
  onSecondaryMenuClick?: (data?: any) => void;
  onUserMenuClick?: (data?: any) => void;
  onActionClick?: (data?: any) => void;

  // 搜索配置
  searchFields?: SearchField[];
  showMoreSearchConditions?: boolean;
  onMoreSearchConditions?: () => void;

  // 工具栏配置
  toolbarActions?: ToolbarAction[];
  quickSearch?: {
    placeholder?: string;
    onSearch?: (value: string) => void;
  };
  toolbarIcons?: Array<{
    key: string;
    icon: string;
    tooltip?: string;
    onClick?: () => void;
  }>;

  // 分组标签
  groupTabs?: GroupTab[];
  onGroupTabChange?: (tabKey: string) => void;
  showCancelGroup?: boolean;
  onCancelGroup?: () => void;

  // 表格配置
  rowActions?: TableAction[];
  pagination?: {
    current: number;
    pageSize: number;
    total: number;
    showSizeChanger?: boolean;
    showQuickJumper?: boolean;
    showTotal?: boolean;
  };
  rowSelection?: {
    type: 'checkbox' | 'radio';
    selectedRowKeys?: string[];
    onChange?: (selectedRowKeys: string[], selectedRows: any[]) => void;
  };

  // 事件回调
  style?: React.CSSProperties;
  className?: string;
  onSearch?: (searchValues: Record<string, any>) => void;
  onTableChange?: (pagination: any, filters: any, sorter: any) => void;
  onRow?: (record: any, index: number) => React.HTMLAttributes<HTMLTableRowElement>;
}

export const TableViewWithSearch: React.FC<TableViewWithSearchProps> = ({
  title = '全部API',
  columns,
  loading = false,
  searchFields = [],
  showMoreSearchConditions = false,
  onMoreSearchConditions,
  toolbarActions = [],
  quickSearch,
  toolbarIcons = [],
  groupTabs = [],
  onGroupTabChange,
  showCancelGroup = false,
  onCancelGroup,
  rowActions = [],
  pagination,
  rowSelection,
  style,
  className,
  onSearch,
  onTableChange,
  onRow,
  componentId,
  events,
  ...rest
}) => {
  // 过滤掉事件处理器和dataSource，避免传递到DOM元素
  const {
    dataSource: _,
    onMount, onRowClick, onPageChange, onToolbarAction, onNodeClick,
    onNodeExpand, onMenuClick, onSecondaryMenuClick, onUserMenuClick,
    onActionClick, ...filteredRest
  } = rest as any;
  const [searchValues, setSearchValues] = React.useState<Record<string, any>>({});
  const [selectedRowKeys, setSelectedRowKeys] = React.useState<string[]>(
    rowSelection?.selectedRowKeys || []
  );

  // 从事件配置获取应用了数据映射的数据源
  const dataSource = useMappedData(componentId, 'onMount', false); // false表示这是表格数据
  const [sortConfig, setSortConfig] = React.useState<{
    key: string;
    direction: 'asc' | 'desc';
  } | null>(null);
  const [quickSearchValue, setQuickSearchValue] = React.useState('');

  // 事件处理
  const eventHandler = useEventHandler(componentId, events);

  // 组件挂载时触发初始加载事件
  React.useEffect(() => {
    eventHandler.onMount({
      componentId,
      timestamp: Date.now()
    });
  }, [componentId, eventHandler]);

  // 处理搜索
  const handleSearch = () => {
    // 触发搜索事件
    eventHandler.onSearch({
      searchValues,
      componentId,
      timestamp: Date.now()
    });
    onSearch?.(searchValues);
  };

  // 重置搜索
  const handleReset = () => {
    setSearchValues({});
    onSearch?.({});
  };

  // 处理搜索字段变化
  const handleSearchFieldChange = (key: string, value: any) => {
    const newValues = { ...searchValues, [key]: value };
    setSearchValues(newValues);
  };

  // 处理行选择
  const handleRowSelection = (keys: string[], rows: any[]) => {
    setSelectedRowKeys(keys);
    rowSelection?.onChange?.(keys, rows);
  };

  // 处理排序
  const handleSort = (column: TableColumn) => {
    if (!column.sortable) return;

    let direction: 'asc' | 'desc' = 'asc';
    if (sortConfig?.key === column.key && sortConfig.direction === 'asc') {
      direction = 'desc';
    }

    setSortConfig({ key: column.key, direction });
    onTableChange?.(pagination, {}, { field: column.key, order: direction });
  };

  // 处理快速搜索
  const handleQuickSearch = () => {
    quickSearch?.onSearch?.(quickSearchValue);
  };

  // 渲染顶部工具栏
  const renderTopToolbar = () => {
    return (
      <div style={{
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'space-between',
        marginBottom: '16px'
      }}>
        <h1 style={{
          fontSize: '18px',
          fontWeight: '600',
          margin: 0,
          color: '#000000',
          lineHeight: '24px'
        }}>
          {title}
        </h1>

        <div style={{
          display: 'flex',
          alignItems: 'center',
          gap: '12px'
        }}>
          {/* 快速搜索 */}
          {quickSearch && (
            <div style={{ position: 'relative' }}>
              <input
                type="text"
                placeholder={quickSearch.placeholder || '请输入URL进行筛选'}
                value={quickSearchValue}
                onChange={(e) => setQuickSearchValue(e.target.value)}
                onKeyDown={(e) => {
                  if (e.key === 'Enter') {
                    handleQuickSearch();
                  }
                }}
                style={{
                  width: '240px',
                  paddingLeft: '12px',
                  paddingRight: '36px',
                  paddingTop: '6px',
                  paddingBottom: '6px',
                  border: '1px solid #d1d5db',
                  borderRadius: '4px',
                  fontSize: '13px',
                  outline: 'none'
                }}
                onFocus={(e) => {
                  e.currentTarget.style.borderColor = '#3b82f6';
                  e.currentTarget.style.boxShadow = '0 0 0 2px rgba(59, 130, 246, 0.2)';
                }}
                onBlur={(e) => {
                  e.currentTarget.style.borderColor = '#d1d5db';
                  e.currentTarget.style.boxShadow = 'none';
                }}
              />
              <span
                style={{
                  position: 'absolute',
                  right: '10px',
                  top: '50%',
                  transform: 'translateY(-50%)',
                  color: '#9ca3af',
                  fontSize: '16px',
                  cursor: 'pointer',
                  fontFamily: 'Material Icons'
                }}
                onClick={handleQuickSearch}
              >
                search
              </span>
            </div>
          )}

          {/* 工具图标 */}
          <div style={{
            display: 'flex',
            alignItems: 'center',
            gap: '6px',
            color: '#6b7280'
          }}>
            {toolbarIcons.map(icon => (
              <span
                key={icon.key}
                onClick={icon.onClick}
                title={icon.tooltip}
                style={{
                  fontFamily: 'Material Icons',
                  fontSize: '18px',
                  cursor: 'pointer',
                  color: '#6b7280'
                }}
                onMouseEnter={(e) => {
                  e.currentTarget.style.color = '#374151';
                }}
                onMouseLeave={(e) => {
                  e.currentTarget.style.color = '#6b7280';
                }}
              >
                {icon.icon}
              </span>
            ))}
          </div>

          {/* 工具栏按钮 */}
          <div style={{
            display: 'flex',
            alignItems: 'center',
            gap: '6px'
          }}>
            {toolbarActions.map(action => (
              <button
                key={action.key}
                onClick={() => {
                  // 触发工具栏操作事件
                  eventHandler.onToolbarAction({
                    action: action.key,
                    actionData: action,
                    componentId,
                    timestamp: Date.now()
                  });
                  // 调用原有的onClick
                  action.onClick?.();
                }}
                disabled={action.disabled}
                style={{
                  padding: '6px 12px',
                  backgroundColor: action.type === 'primary' ? '#3b82f6' : '#f3f4f6',
                  color: action.type === 'primary' ? '#ffffff' : '#374151',
                  border: 'none',
                  borderRadius: '4px',
                  cursor: action.disabled ? 'not-allowed' : 'pointer',
                  fontSize: '13px',
                  fontWeight: '400',
                  opacity: action.disabled ? 0.6 : 1,
                  display: 'flex',
                  alignItems: 'center',
                  gap: '3px'
                }}
                onMouseEnter={(e) => {
                  if (!action.disabled) {
                    e.currentTarget.style.backgroundColor = action.type === 'primary' ? '#2563eb' : '#e5e7eb';
                  }
                }}
                onMouseLeave={(e) => {
                  if (!action.disabled) {
                    e.currentTarget.style.backgroundColor = action.type === 'primary' ? '#3b82f6' : '#f3f4f6';
                  }
                }}
              >
                {action.label}
                {action.key === 'batch' && (
                  <span style={{
                    fontFamily: 'Material Icons',
                    fontSize: '14px',
                    marginLeft: '2px'
                  }}>
                    arrow_drop_down
                  </span>
                )}
              </button>
            ))}
          </div>
        </div>
      </div>
    );
  };

  // 渲染搜索表单
  const renderSearchForm = () => {
    if (searchFields.length === 0) return null;

    return (
      <div style={{
        backgroundColor: '#f3f4f5',
        padding: '32px 24px 24px 24px',
        borderRadius: '8px',
        marginBottom: '16px'
      }}>
        <div style={{
          display: 'grid',
          gridTemplateColumns: 'repeat(2, 1fr)',
          gap: '12px 32px',
          marginBottom: '16px'
        }}>
          {searchFields.map(field => (
            <div key={field.key} style={{
              display: 'flex',
              alignItems: 'center'
            }}>
              <label style={{
                width: '90px',
                color: '#6b7280',
                fontSize: '13px',
                flexShrink: 0
              }}>
                {field.label}
              </label>

              {field.type === 'input' && (
                <input
                  type="text"
                  placeholder={field.placeholder || '请输入'}
                  value={searchValues[field.key] || ''}
                  onChange={(e) => handleSearchFieldChange(field.key, e.target.value)}
                  style={{
                    width: '100%',
                    padding: '6px 10px',
                    border: '1px solid #d1d5db',
                    borderRadius: '4px',
                    fontSize: '13px',
                    outline: 'none',
                    backgroundColor: '#ffffff'
                  }}
                />
              )}

              {field.type === 'select' && (
                <select
                  value={searchValues[field.key] || ''}
                  onChange={(e) => handleSearchFieldChange(field.key, e.target.value)}
                  style={{
                    width: '100%',
                    padding: '6px 24px 6px 10px',
                    border: '1px solid #d1d5db',
                    borderRadius: '4px',
                    fontSize: '13px',
                    backgroundColor: '#ffffff',
                    outline: 'none',
                    appearance: 'none',
                    backgroundImage: `url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6,9 12,15 18,9'%3e%3c/polyline%3e%3c/svg%3e")`,
                    backgroundRepeat: 'no-repeat',
                    backgroundPosition: 'right 8px center',
                    backgroundSize: '16px'
                  }}
                >
                  <option value="">{field.placeholder || '请选择'}</option>
                  {field.options?.map(option => (
                    <option key={option.value} value={option.value}>
                      {option.label}
                    </option>
                  ))}
                </select>
              )}

              {field.type === 'combinedSelect' && (
                <div style={{
                  display: 'flex',
                  alignItems: 'center',
                  width: '100%'
                }}>
                  <select
                    value={searchValues[`${field.key}_left`] || ''}
                    onChange={(e) => handleSearchFieldChange(`${field.key}_left`, e.target.value)}
                    style={{
                      width: '33.33%',
                      padding: '6px 24px 6px 10px',
                      border: '1px solid #d1d5db',
                      borderRight: 'none',
                      borderTopLeftRadius: '4px',
                      borderBottomLeftRadius: '4px',
                      fontSize: '13px',
                      backgroundColor: '#ffffff',
                      outline: 'none',
                      appearance: 'none',
                      backgroundImage: `url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6,9 12,15 18,9'%3e%3c/polyline%3e%3c/svg%3e")`,
                      backgroundRepeat: 'no-repeat',
                      backgroundPosition: 'right 8px center',
                      backgroundSize: '16px'
                    }}
                  >
                    {field.combinedConfig?.leftOptions?.map(option => (
                      <option key={option.value} value={option.value}>
                        {option.label}
                      </option>
                    ))}
                  </select>
                  <select
                    value={searchValues[`${field.key}_right`] || ''}
                    onChange={(e) => handleSearchFieldChange(`${field.key}_right`, e.target.value)}
                    style={{
                      width: '66.67%',
                      padding: '6px 24px 6px 10px',
                      border: '1px solid #d1d5db',
                      borderTopRightRadius: '4px',
                      borderBottomRightRadius: '4px',
                      fontSize: '13px',
                      backgroundColor: '#ffffff',
                      outline: 'none',
                      appearance: 'none',
                      backgroundImage: `url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6,9 12,15 18,9'%3e%3c/polyline%3e%3c/svg%3e")`,
                      backgroundRepeat: 'no-repeat',
                      backgroundPosition: 'right 8px center',
                      backgroundSize: '16px'
                    }}
                  >
                    <option value="">{field.placeholder || '请选择'}</option>
                    {field.combinedConfig?.rightOptions?.map(option => (
                      <option key={option.value} value={option.value}>
                        {option.label}
                      </option>
                    ))}
                  </select>
                </div>
              )}

              {field.type === 'checkbox' && field.checkboxConfig && (
                <div style={{
                  display: 'flex',
                  alignItems: 'center',
                  width: '100%',
                  gap: '16px'
                }}>
                  <div style={{
                    display: 'flex',
                    alignItems: 'center',
                    flexGrow: 1
                  }}>
                    <select
                      value={searchValues[`${field.key}_select`] || ''}
                      onChange={(e) => handleSearchFieldChange(`${field.key}_select`, e.target.value)}
                      style={{
                        width: '33.33%',
                        padding: '6px 24px 6px 10px',
                        border: '1px solid #d1d5db',
                        borderTopLeftRadius: '4px',
                        borderBottomLeftRadius: '4px',
                        borderRight: 'none',
                        fontSize: '13px',
                        backgroundColor: '#ffffff',
                        outline: 'none',
                        appearance: 'none',
                        backgroundImage: `url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6,9 12,15 18,9'%3e%3c/polyline%3e%3c/svg%3e")`,
                        backgroundRepeat: 'no-repeat',
                        backgroundPosition: 'right 8px center',
                        backgroundSize: '16px'
                      }}
                    >
                      <option>或</option>
                      {field.options?.map(option => (
                        <option key={option.value} value={option.value}>
                          {option.label}
                        </option>
                      ))}
                    </select>
                    <select
                      value={searchValues[`${field.key}_right`] || ''}
                      onChange={(e) => handleSearchFieldChange(`${field.key}_right`, e.target.value)}
                      style={{
                        width: '66.67%',
                        padding: '6px 24px 6px 10px',
                        border: '1px solid #d1d5db',
                        borderTopRightRadius: '4px',
                        borderBottomRightRadius: '4px',
                        fontSize: '13px',
                        backgroundColor: '#ffffff',
                        outline: 'none',
                        appearance: 'none',
                        backgroundImage: `url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6,9 12,15 18,9'%3e%3c/polyline%3e%3c/svg%3e")`,
                        backgroundRepeat: 'no-repeat',
                        backgroundPosition: 'right 8px center',
                        backgroundSize: '16px'
                      }}
                    >
                      <option value="">{field.placeholder || '请选择'}</option>
                      {field.options?.map(option => (
                        <option key={option.value} value={option.value}>
                          {option.label}
                        </option>
                      ))}
                    </select>
                  </div>
                  <div style={{
                    display: 'flex',
                    alignItems: 'center',
                    flexShrink: 0,
                    whiteSpace: 'nowrap'
                  }}>
                    <input
                      type="checkbox"
                      checked={searchValues[`${field.key}_checkbox`] || false}
                      onChange={(e) => handleSearchFieldChange(`${field.key}_checkbox`, e.target.checked)}
                      style={{ marginRight: '8px' }}
                    />
                    <span style={{
                      color: '#6b7280',
                      fontSize: '13px'
                    }}>
                      {field.checkboxConfig.text}
                    </span>
                    {field.checkboxConfig.tooltip && (
                      <span style={{
                        marginLeft: '4px',
                        color: '#9ca3af',
                        fontSize: '16px',
                        fontFamily: 'Material Icons'
                      }}>
                        info
                      </span>
                    )}
                  </div>
                </div>
              )}

              {field.type === 'dateRange' && (
                <div style={{
                  display: 'flex',
                  alignItems: 'center',
                  gap: '8px'
                }}>
                  <select
                    style={{
                      padding: '6px 24px 6px 10px',
                      border: '1px solid #d1d5db',
                      borderRadius: '4px',
                      fontSize: '13px',
                      backgroundColor: '#ffffff',
                      outline: 'none',
                      appearance: 'none',
                      backgroundImage: `url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6,9 12,15 18,9'%3e%3c/polyline%3e%3c/svg%3e")`,
                      backgroundRepeat: 'no-repeat',
                      backgroundPosition: 'right 8px center',
                      backgroundSize: '16px'
                    }}
                  >
                    <option>自定义时间</option>
                  </select>
                  <input
                    type="text"
                    placeholder="开始日期"
                    style={{
                      width: '140px',
                      padding: '6px 10px',
                      border: '1px solid #d1d5db',
                      borderRadius: '4px',
                      fontSize: '13px',
                      backgroundColor: '#ffffff'
                    }}
                  />
                  <span style={{ color: '#9ca3af', fontSize: '13px' }}>-</span>
                  <input
                    type="text"
                    placeholder="结束日期"
                    style={{
                      width: '140px',
                      padding: '6px 10px',
                      border: '1px solid #d1d5db',
                      borderRadius: '4px',
                      fontSize: '13px',
                      backgroundColor: '#ffffff'
                    }}
                  />
                </div>
              )}
            </div>
          ))}
        </div>

        <div style={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'flex-end',
          gap: '8px',
          marginTop: '12px'
        }}>
          {showMoreSearchConditions && (
            <button
              onClick={onMoreSearchConditions}
              style={{
                color: '#3b82f6',
                background: 'none',
                border: 'none',
                cursor: 'pointer',
                fontSize: '14px',
                marginRight: '16px'
              }}
            >
              更多搜索条件
            </button>
          )}
          <button
            onClick={handleReset}
            style={{
              padding: '6px 14px',
              backgroundColor: '#e5e7eb',
              color: '#374151',
              border: 'none',
              borderRadius: '4px',
              cursor: 'pointer',
              fontSize: '13px'
            }}
          >
            取消
          </button>
          <button
            onClick={handleSearch}
            style={{
              padding: '6px 14px',
              backgroundColor: '#3b82f6',
              color: '#ffffff',
              border: 'none',
              borderRadius: '4px',
              cursor: 'pointer',
              fontSize: '13px'
            }}
          >
            确定
          </button>
        </div>
      </div>
    );
  };

  // 渲染分组标签
  const renderGroupTabs = () => {
    if (groupTabs.length === 0) return null;

    return (
      <div style={{
        display: 'flex',
        alignItems: 'center',
        gap: '24px',
        marginBottom: '16px'
      }}>
        <span style={{
          color: '#6b7280',
          fontSize: '14px'
        }}>
          按API风险等级分组:
        </span>

        <div style={{
          display: 'flex',
          alignItems: 'center',
          gap: '24px'
        }}>
          {groupTabs.map(tab => (
            <button
              key={tab.key}
              onClick={() => onGroupTabChange?.(tab.key)}
              style={{
                background: 'none',
                border: 'none',
                padding: '8px 0',
                cursor: 'pointer',
                fontSize: '14px',
                fontWeight: tab.active ? '600' : '400',
                color: tab.active ? '#3b82f6' : '#6b7280',
                borderBottom: tab.active ? '2px solid #3b82f6' : '2px solid transparent',
                transition: 'all 0.2s'
              }}
              onMouseEnter={(e) => {
                if (!tab.active) {
                  e.currentTarget.style.color = '#374151';
                }
              }}
              onMouseLeave={(e) => {
                if (!tab.active) {
                  e.currentTarget.style.color = '#6b7280';
                }
              }}
            >
              {tab.label} {tab.count !== undefined && `(${tab.count})`}
            </button>
          ))}

          {showCancelGroup && (
            <button
              onClick={onCancelGroup}
              style={{
                background: 'none',
                border: 'none',
                padding: '8px 0',
                cursor: 'pointer',
                fontSize: '14px',
                color: '#3b82f6',
                marginLeft: '16px'
              }}
            >
              取消分组
            </button>
          )}
        </div>
      </div>
    );
  };

  // 渲染表格
  const renderTable = () => {
    return (
      <div style={{
        overflow: 'hidden',
        backgroundColor: '#ffffff'
      }}>
        <table style={{ width: '100%', borderCollapse: 'collapse' }}>
          <thead style={{ backgroundColor: '#eaedf4' }}>
            <tr>
              {rowSelection && (
                <th style={{
                  padding: '12px',
                  textAlign: 'left',
                  width: '48px',
                  fontWeight: 'normal'
                }}>
                  <input
                    type={rowSelection.type === 'radio' ? 'radio' : 'checkbox'}
                    checked={selectedRowKeys.length === dataSource.length && dataSource.length > 0}
                    onChange={(e) => {
                      if (e.target.checked) {
                        const allKeys = dataSource.map((_: any, index: number) => index.toString());
                        handleRowSelection(allKeys, dataSource);
                      } else {
                        handleRowSelection([], []);
                      }
                    }}
                  />
                </th>
              )}
              {columns.map(column => (
                <th
                  key={column.key}
                  style={{
                    padding: '12px',
                    textAlign: column.align || 'left',
                    width: column.width,
                    cursor: column.sortable ? 'pointer' : 'default',
                    userSelect: 'none',
                    whiteSpace: 'nowrap',
                    fontWeight: 'normal',
                    color: '#6b7280',
                    fontSize: '14px'
                  }}
                  onClick={() => handleSort(column)}
                >
                  <div style={{ display: 'flex', alignItems: 'center', gap: '4px' }}>
                    <span>{column.title}</span>
                    {column.sortable && (
                      <span style={{ fontSize: '12px' }}>
                        {sortConfig?.key === column.key ? (
                          sortConfig.direction === 'asc' ? '↑' : '↓'
                        ) : '↕'}
                      </span>
                    )}
                    {(column.key === 'totalVisits' || column.key === 'accessCount') && (
                      <span style={{
                        color: '#9ca3af',
                        fontSize: '16px',
                        fontFamily: 'Material Icons'
                      }}>
                        info
                      </span>
                    )}
                  </div>
                </th>
              ))}
              {rowActions.length > 0 && (
                <th style={{
                  padding: '12px',
                  textAlign: 'center',
                  fontWeight: 'normal',
                  color: '#6b7280',
                  fontSize: '14px'
                }}>
                  操作
                </th>
              )}
            </tr>
          </thead>
          <tbody>
            {loading ? (
              <tr>
                <td
                  colSpan={columns.length + (rowSelection ? 1 : 0) + (rowActions.length > 0 ? 1 : 0)}
                  style={{
                    padding: '32px',
                    textAlign: 'center',
                    color: '#666',
                    backgroundColor: '#fff'
                  }}
                >
                  加载中...
                </td>
              </tr>
            ) : dataSource.length === 0 ? (
              <tr>
                <td
                  colSpan={columns.length + (rowSelection ? 1 : 0) + (rowActions.length > 0 ? 1 : 0)}
                  style={{
                    padding: '32px',
                    textAlign: 'center',
                    color: '#666',
                    backgroundColor: '#fff'
                  }}
                >
                  暂无数据
                </td>
              </tr>
            ) : (
              dataSource.map((record: any, index: number) => {
                const rowKey = index.toString();
                const isSelected = selectedRowKeys.includes(rowKey);
                const rowProps = onRow?.(record, index) || {};

                // 添加行点击事件处理
                const handleRowClick = (e: React.MouseEvent<HTMLTableRowElement>) => {
                  // 触发行点击事件
                  eventHandler.onRowClick({
                    record,
                    index,
                    componentId,
                    timestamp: Date.now()
                  });

                  // 如果原有的rowProps有onClick，也要调用
                  if (rowProps.onClick) {
                    rowProps.onClick(e);
                  }
                };

                // 合并点击事件
                const mergedRowProps = {
                  ...rowProps,
                  onClick: handleRowClick,
                  style: {
                    cursor: 'pointer',
                    ...rowProps.style
                  }
                };

                return (
                  <tr
                    key={rowKey}
                    {...mergedRowProps}
                    style={{
                      backgroundColor: isSelected ? '#eef5ff' : 'transparent',
                      ...rowProps.style
                    }}
                    onMouseEnter={(e) => {
                      if (!isSelected) {
                        e.currentTarget.style.backgroundColor = '#f8fafc';
                      }
                      rowProps.onMouseEnter?.(e);
                    }}
                    onMouseLeave={(e) => {
                      if (!isSelected) {
                        e.currentTarget.style.backgroundColor = 'transparent';
                      }
                      rowProps.onMouseLeave?.(e);
                    }}
                  >
                    {rowSelection && (
                      <td style={{
                        padding: '12px',
                        borderBottom: '1px solid #e5e7eb'
                      }}>
                        <input
                          type={rowSelection.type === 'radio' ? 'radio' : 'checkbox'}
                          checked={isSelected}
                          onChange={(e) => {
                            if (rowSelection.type === 'radio') {
                              handleRowSelection([rowKey], [record]);
                            } else {
                              if (e.target.checked) {
                                handleRowSelection([...selectedRowKeys, rowKey], [...selectedRowKeys.map(key => dataSource[parseInt(key)]), record]);
                              } else {
                                const newKeys = selectedRowKeys.filter(key => key !== rowKey);
                                handleRowSelection(newKeys, newKeys.map(key => dataSource[parseInt(key)]));
                              }
                            }
                          }}
                        />
                      </td>
                    )}
                    {columns.map(column => {
                      const value = record[column.dataIndex];
                      let cellContent = column.render ? column.render(value, record, index) : value;

                      // 特殊处理路径列，添加敏感度标签
                      if (column.key === 'path' && record.sensitivity) {
                        cellContent = (
                          <div style={{ display: 'flex', alignItems: 'center' }}>
                            <span style={{
                              backgroundColor: '#fef2f2',
                              color: '#dc2626',
                              fontSize: '12px',
                              fontWeight: '600',
                              marginRight: '8px',
                              padding: '2px 8px',
                              borderRadius: '4px'
                            }}>
                              {record.sensitivity}
                            </span>
                            <span style={{ overflow: 'hidden', textOverflow: 'ellipsis' }}>
                              {value}
                            </span>
                          </div>
                        );
                      }

                      // 特殊处理访问次数列，添加蓝色样式
                      if ((column.key === 'totalVisits' || column.key === 'accessCount') && typeof cellContent === 'string') {
                        cellContent = (
                          <span style={{ color: '#3b82f6' }}>
                            {cellContent}
                          </span>
                        );
                      }

                      return (
                        <td
                          key={column.key}
                          style={{
                            padding: '12px',
                            textAlign: column.align || 'left',
                            borderBottom: '1px solid #e5e7eb',
                            fontSize: '14px',
                            color: '#374151'
                          }}
                        >
                          {cellContent}
                        </td>
                      );
                    })}
                    {rowActions.length > 0 && (
                      <td style={{
                        padding: '12px',
                        textAlign: 'center',
                        borderBottom: '1px solid #e5e7eb'
                      }}>
                        <div style={{
                          display: 'flex',
                          gap: '8px',
                          justifyContent: 'center',
                          alignItems: 'center'
                        }}>
                          {rowActions.map(action => (
                            <span
                              key={action.key}
                              onClick={() => action.onClick?.(record, index)}
                              style={{
                                fontFamily: 'Material Icons',
                                fontSize: '20px',
                                color: '#6b7280',
                                cursor: action.disabled ? 'not-allowed' : 'pointer',
                                opacity: action.disabled ? 0.6 : 1
                              }}
                              onMouseEnter={(e) => {
                                if (!action.disabled) {
                                  e.currentTarget.style.color = '#374151';
                                }
                              }}
                              onMouseLeave={(e) => {
                                if (!action.disabled) {
                                  e.currentTarget.style.color = '#6b7280';
                                }
                              }}
                            >
                              {action.icon || (action.key === 'copy' ? 'content_copy' : 'more_horiz')}
                            </span>
                          ))}
                        </div>
                      </td>
                    )}
                  </tr>
                );
              })
            )}
          </tbody>
        </table>
      </div>
    );
  };

  // 渲染分页
  const renderPagination = () => {
    if (!pagination) return null;

    const { current, pageSize, total, showSizeChanger, showTotal } = pagination;
    const totalPages = Math.ceil(total / pageSize);

    return (
      <div style={{
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginTop: '16px'
      }}>
        {showTotal && (
          <span style={{
            fontSize: '14px',
            color: '#6b7280'
          }}>
            共 {total} 条 第 {current} / {totalPages} 页
          </span>
        )}

        <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
          <button
            disabled={current <= 1}
            onClick={() => onTableChange?.({ ...pagination, current: current - 1 }, {}, {})}
            style={{
              padding: '8px',
              border: '1px solid #d1d5db',
              borderRadius: '6px',
              backgroundColor: '#ffffff',
              cursor: current <= 1 ? 'not-allowed' : 'pointer',
              opacity: current <= 1 ? 0.5 : 1,
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center'
            }}
          >
            <span style={{
              fontFamily: 'Material Icons',
              fontSize: '16px',
              color: '#6b7280'
            }}>
              chevron_left
            </span>
          </button>

          <button
            disabled={current >= totalPages}
            onClick={() => onTableChange?.({ ...pagination, current: current + 1 }, {}, {})}
            style={{
              padding: '8px',
              border: '1px solid #d1d5db',
              borderRadius: '6px',
              backgroundColor: '#ffffff',
              cursor: current >= totalPages ? 'not-allowed' : 'pointer',
              opacity: current >= totalPages ? 0.5 : 1,
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center'
            }}
          >
            <span style={{
              fontFamily: 'Material Icons',
              fontSize: '16px',
              color: '#6b7280'
            }}>
              chevron_right
            </span>
          </button>

          {showSizeChanger && (
            <select
              value={pageSize}
              onChange={(e) => {
                const newPageSize = parseInt(e.target.value);
                onTableChange?.({ ...pagination, pageSize: newPageSize, current: 1 }, {}, {});
              }}
              style={{
                marginLeft: '16px',
                padding: '8px 24px 8px 8px',
                border: '1px solid #d1d5db',
                borderRadius: '6px',
                fontSize: '14px',
                backgroundColor: '#ffffff',
                outline: 'none',
                appearance: 'none',
                backgroundImage: `url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6,9 12,15 18,9'%3e%3c/polyline%3e%3c/svg%3e")`,
                backgroundRepeat: 'no-repeat',
                backgroundPosition: 'right 8px center',
                backgroundSize: '16px'
              }}
            >
              <option value={25}>25条/页</option>
              <option value={50}>50条/页</option>
              <option value={100}>100条/页</option>
            </select>
          )}
        </div>
      </div>
    );
  };

  const defaultStyle: React.CSSProperties = {
    backgroundColor: '#ffffff',
    borderRadius: 0,
    padding: '12px',
    ...style,
  };

  return (
    <div className={`lowcode-table-view-with-search ${className || ''}`} style={defaultStyle} {...filteredRest}>
      <div style={{
        backgroundColor: '#ffffff',
        padding: '4px 16px 16px 16px',
        borderRadius: '8px',
        boxShadow: '0 1px 3px 0 rgba(0, 0, 0, 0.1)'
      }}>
        {renderTopToolbar()}
        {renderSearchForm()}
        {renderGroupTabs()}
        {renderTable()}
        {renderPagination()}
      </div>
    </div>
  );
};
