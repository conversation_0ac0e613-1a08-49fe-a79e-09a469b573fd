#!/bin/bash

# 低代码平台启动脚本
# 该脚本会重新构建所有包并启动三个应用

set -e  # 遇到错误时退出

echo "🚀 开始启动低代码平台..."
echo "================================"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查 Node.js 和 npm
check_dependencies() {
    log_info "检查依赖..."
    
    if ! command -v node &> /dev/null; then
        log_error "Node.js 未安装，请先安装 Node.js"
        exit 1
    fi
    
    if ! command -v npm &> /dev/null; then
        log_error "npm 未安装，请先安装 npm"
        exit 1
    fi
    
    log_success "依赖检查通过"
}

# 安装根目录依赖
install_root_dependencies() {
    log_info "安装根目录依赖..."
    npm install
    log_success "根目录依赖安装完成"
}

# 构建共享包
build_packages() {
    log_info "开始构建共享包..."
    
    # 构建 shared 包
    log_info "构建 @lowcode/shared..."
    cd packages/shared
    npm install
    npm run build
    cd ../..
    log_success "@lowcode/shared 构建完成"
    
    # 构建 renderer 包
    log_info "构建 @lowcode/renderer..."
    cd packages/renderer
    npm install
    npm run build
    cd ../..
    log_success "@lowcode/renderer 构建完成"
    
    # 构建 designer 包
    log_info "构建 @lowcode/designer..."
    cd packages/designer
    npm install
    npm run build
    cd ../..
    log_success "@lowcode/designer 构建完成"
    
    log_success "所有共享包构建完成"
}

# 安装应用依赖
install_app_dependencies() {
    log_info "安装应用依赖..."
    
    # 安装服务器依赖
    log_info "安装服务器应用依赖..."
    cd apps/low_server
    npm install
    cd ../..

    # 安装设计器依赖
    log_info "安装设计器应用依赖..."
    cd apps/low_design
    npm install
    cd ../..

    # 安装运行时依赖
    log_info "安装运行时应用依赖..."
    cd apps/low_runtime
    npm install
    cd ../..
    
    log_success "所有应用依赖安装完成"
}

# 创建 PID 文件目录
create_pid_dir() {
    mkdir -p .pids
}

# 启动服务器
start_server() {
    log_info "启动服务器 (端口 3005)..."
    cd apps/low_server
    npm run dev > ../../logs/server.log 2>&1 &
    SERVER_PID=$!
    echo $SERVER_PID > ../../.pids/server.pid
    cd ../..
    log_success "服务器已启动 (PID: $SERVER_PID)"
}

# 启动设计器应用
start_design_app() {
    log_info "启动设计器应用 (端口 3001)..."
    cd apps/low_design
    npm run dev > ../../logs/design.log 2>&1 &
    DESIGN_PID=$!
    echo $DESIGN_PID > ../../.pids/design.pid
    cd ../..
    log_success "设计器应用已启动 (PID: $DESIGN_PID)"
}

# 启动运行时应用
start_runtime_app() {
    log_info "启动运行时应用 (端口 3003)..."
    cd apps/low_runtime
    npm run dev > ../../logs/runtime.log 2>&1 &
    RUNTIME_PID=$!
    echo $RUNTIME_PID > ../../.pids/runtime.pid
    cd ../..
    log_success "运行时应用已启动 (PID: $RUNTIME_PID)"
}

# 等待服务启动
wait_for_services() {
    log_info "等待服务启动..."
    sleep 5
    log_success "服务启动完成"
}

# 显示访问信息
show_access_info() {
    echo ""
    echo "================================"
    log_success "🎉 低代码平台启动成功！"
    echo "================================"
    echo ""
    echo "📱 应用访问地址："
    echo "   🎨 设计器:    http://localhost:3001"
    echo "   🚀 运行时:    http://localhost:3003"
    echo "   🔧 服务器:    http://localhost:3005"
    echo ""
    echo "📋 管理命令："
    echo "   停止服务:    ./stop.sh"
    echo "   查看日志:    tail -f logs/*.log"
    echo ""
    echo "📁 日志文件："
    echo "   服务器日志:  logs/server.log"
    echo "   设计器日志:  logs/design.log"
    echo "   运行时日志:  logs/runtime.log"
    echo ""
}

# 主函数
main() {
    # 创建日志目录
    mkdir -p logs
    
    # 清理旧的日志文件
    rm -f logs/*.log
    
    check_dependencies
    create_pid_dir
    install_root_dependencies
    build_packages
    install_app_dependencies
    
    echo ""
    log_info "开始启动应用服务..."
    
    start_server
    sleep 2  # 等待服务器启动
    start_design_app
    sleep 1
    start_runtime_app
    
    wait_for_services
    show_access_info
}

# 捕获中断信号，确保清理
trap 'log_warning "启动过程被中断"; exit 1' INT

# 执行主函数
main
