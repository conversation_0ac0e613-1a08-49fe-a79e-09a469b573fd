import React, { useMemo, useRef } from 'react';
import { createRenderer } from '@lowcode/renderer';
import { ComponentMeta, PropMeta, ComponentSchema, findComponent } from '@lowcode/shared';
import { useDesigner } from '../../context/DesignerContext';
import { JsonEditor } from './JsonEditor';

export interface PropertyPanelProps {
  width?: number;
  style?: React.CSSProperties;
  className?: string;
}

const Field: React.FC<{
  meta: PropMeta;
  value: any;
  onChange: (v: any) => void;
}> = ({ meta, value, onChange }) => {
  const inputStyle: React.CSSProperties = {
    width: '100%',
    boxSizing: 'border-box',
    padding: '8px 12px',
    border: '1px solid #d9d9d9',
    borderRadius: '6px',
    fontSize: '14px',
    transition: 'border-color 0.3s ease',
    outline: 'none'
  };

  const focusStyle = {
    borderColor: '#1890ff',
    boxShadow: '0 0 0 2px rgba(24, 144, 255, 0.2)'
  };

  switch (meta.type) {
    case 'string':
      return (
        <input
          style={inputStyle}
          type="text"
          value={value ?? ''}
          onChange={(e) => onChange(e.target.value)}
          onFocus={(e) => Object.assign(e.target.style, focusStyle)}
          onBlur={(e) => {
            e.target.style.borderColor = '#d9d9d9';
            e.target.style.boxShadow = 'none';
          }}
          placeholder={meta.description || '请输入文本'}
        />
      );
    case 'number':
      return (
        <input
          style={inputStyle}
          type="number"
          value={value ?? ''}
          onChange={(e) => {
            const v = e.target.value;
            onChange(v === '' ? undefined : Number(v));
          }}
          onFocus={(e) => Object.assign(e.target.style, focusStyle)}
          onBlur={(e) => {
            e.target.style.borderColor = '#d9d9d9';
            e.target.style.boxShadow = 'none';
          }}
          placeholder={meta.description || '请输入数字'}
        />
      );
    case 'boolean':
      return (
        <label style={{
          display: 'inline-flex',
          alignItems: 'center',
          gap: 8,
          padding: '8px 12px',
          border: '1px solid #d9d9d9',
          borderRadius: '6px',
          backgroundColor: '#fafafa',
          cursor: 'pointer',
          transition: 'all 0.3s ease'
        }}>
          <input
            type="checkbox"
            checked={!!value}
            onChange={(e) => onChange(e.target.checked)}
            style={{ margin: 0 }}
          />
          <span style={{ fontSize: '14px', color: value ? '#52c41a' : '#999' }}>
            {value ? '是' : '否'}
          </span>
        </label>
      );
    case 'object':
    case 'array':
      return (
        <JsonEditor
          value={value}
          onChange={onChange}
          placeholder={`请输入${meta.type === 'object' ? '对象' : '数组'}（JSON格式）`}
          height={180}
        />
      );
    case 'function':
    default:
      return (
        <input
          style={{...inputStyle, fontFamily: 'monospace'}}
          type="text"
          value={value ?? ''}
          onChange={(e) => onChange(e.target.value)}
          placeholder={`不支持的类型(${meta.type})，以字符串方式编辑`}
          onFocus={(e) => Object.assign(e.target.style, focusStyle)}
          onBlur={(e) => {
            e.target.style.borderColor = '#d9d9d9';
            e.target.style.boxShadow = 'none';
          }}
        />
      );
  }
};

export const PropertyPanel: React.FC<PropertyPanelProps> = ({ width = 576, style, className }) => {
  const { schema, canvasState, updateComponent } = useDesigner();
  const rendererRef = useRef(createRenderer());

  const selectedComponent: ComponentSchema | null = useMemo(() => {
    if (!canvasState.selectedComponentId) return null;
    return findComponent(schema.components, canvasState.selectedComponentId);
  }, [schema.components, canvasState.selectedComponentId]);

  const meta: ComponentMeta | undefined = useMemo(() => {
    if (!selectedComponent) return undefined;
    return rendererRef.current.getComponentRegistry().getMeta(selectedComponent.type);
  }, [selectedComponent]);

  const defaultStyle: React.CSSProperties = {
    width,
    height: '100%',
    backgroundColor: '#f8f9fa',
    borderLeft: '1px solid #e8e8e8',
    display: 'flex',
    flexDirection: 'column',
    fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif',
    ...style
  };

  const handlePropChange = (name: string, value: any) => {
    if (!selectedComponent) return;
    const nextProps = { ...(selectedComponent.props || {}) } as Record<string, any>;
    if (value === undefined) {
      delete nextProps[name];
    } else {
      nextProps[name] = value;
    }
    updateComponent(selectedComponent.id, { props: nextProps });
  };

  const getInitialValue = (p: PropMeta) => {
    const current = selectedComponent?.props?.[p.name];
    if (current !== undefined) return current;
    return p.default;
  };

  if (!selectedComponent) {
    return (
      <div className={`lowcode-property-panel ${className || ''}`} style={defaultStyle}>
        <div style={{ padding: 16, color: '#999' }}>请选择画布中的组件查看属性</div>
      </div>
    );
  }

  return (
    <div className={`lowcode-property-panel ${className || ''}`} style={defaultStyle}>
      {/* 标题 */}
      <div
        style={{
          padding: '20px 24px',
          borderBottom: '1px solid #e8e8e8',
          backgroundColor: '#ffffff',
          boxShadow: '0 1px 3px rgba(0,0,0,0.1)'
        }}
      >
        <div style={{ fontSize: 12, color: '#8c8c8c', textTransform: 'uppercase', letterSpacing: '0.5px', marginBottom: 4 }}>
          已选组件
        </div>
        <div style={{ fontSize: 18, fontWeight: 600, color: '#262626', marginBottom: 4 }}>
          {meta?.name || selectedComponent.type}
        </div>
        <div style={{ fontSize: 12, color: '#8c8c8c', fontFamily: 'monospace' }}>
          ID: {selectedComponent.id}
        </div>
      </div>

      {/* 属性编辑区 */}
      <div style={{ flex: 1, overflow: 'auto', padding: '24px' }}>
        {(meta?.props || []).length > 0 ? (
          <div style={{ marginBottom: 32 }}>
            <h3 style={{
              fontSize: 16,
              fontWeight: 600,
              color: '#262626',
              marginBottom: 16,
              borderBottom: '2px solid #1890ff',
              paddingBottom: 8
            }}>
              组件属性
            </h3>
            {(meta?.props || []).map((p) => (
              <div key={p.name} style={{
                marginBottom: 24,
                padding: '16px',
                backgroundColor: '#fff',
                borderRadius: '8px',
                border: '1px solid #f0f0f0',
                boxShadow: '0 1px 2px rgba(0,0,0,0.03)'
              }}>
                <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: 8 }}>
                  <label style={{
                    fontSize: 14,
                    fontWeight: 600,
                    color: '#262626',
                    display: 'flex',
                    alignItems: 'center',
                    gap: 8
                  }}>
                    {p.name}
                    <span style={{
                      fontSize: 11,
                      color: '#8c8c8c',
                      backgroundColor: '#f5f5f5',
                      padding: '2px 6px',
                      borderRadius: '4px',
                      fontFamily: 'monospace'
                    }}>
                      {p.type}
                    </span>
                  </label>
                  {p.required && (
                    <span style={{
                      color: '#ff4d4f',
                      fontSize: 11,
                      backgroundColor: '#fff2f0',
                      padding: '2px 6px',
                      borderRadius: '4px',
                      border: '1px solid #ffccc7'
                    }}>
                      必填
                    </span>
                  )}
                </div>
                {p.description && (
                  <div style={{
                    color: '#8c8c8c',
                    fontSize: 12,
                    marginBottom: 12,
                    lineHeight: 1.5,
                    fontStyle: 'italic'
                  }}>
                    {p.description}
                  </div>
                )}
                <Field meta={p} value={getInitialValue(p)} onChange={(v) => handlePropChange(p.name, v)} />
              </div>
            ))}
          </div>
        ) : (
          <div style={{
            textAlign: 'center',
            padding: '40px 20px',
            color: '#8c8c8c',
            fontSize: 14
          }}>
            该组件暂无可配置属性
          </div>
        )}


      </div>
    </div>
  );
};

