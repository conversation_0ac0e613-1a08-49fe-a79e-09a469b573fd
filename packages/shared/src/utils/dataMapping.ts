/**
 * 数据映射工具函数
 * 用于根据配置的映射关系转换API响应数据
 */

export type DataMapping = Record<string, string>;

/**
 * 根据路径获取对象中的值
 * 支持嵌套路径，如 'user.profile.name' 或 'items[0].title'
 */
export function getValueByPath(obj: any, path: string): any {
  if (!obj || !path) return undefined;
  
  try {
    // 处理数组索引，如 items[0].title
    const normalizedPath = path.replace(/\[(\d+)\]/g, '.$1');
    const keys = normalizedPath.split('.');
    
    let current = obj;
    for (const key of keys) {
      if (current === null || current === undefined) {
        return undefined;
      }
      current = current[key];
    }
    
    return current;
  } catch (error) {
    console.warn(`Failed to get value by path "${path}":`, error);
    return undefined;
  }
}

/**
 * 应用数据映射到单个数据项
 * @param sourceData 源数据对象
 * @param mapping 映射配置：目标字段 -> 源字段路径
 * @returns 映射后的数据对象
 */
export function applyDataMapping(sourceData: any, mapping: DataMapping): any {
  if (!sourceData || !mapping || Object.keys(mapping).length === 0) {
    return {}; // 没有映射配置时返回空对象
  }

  const result: any = {};

  // 只应用映射，不复制原始字段
  Object.entries(mapping).forEach(([targetField, sourcePath]) => {
    if (sourcePath) {
      const value = getValueByPath(sourceData, sourcePath);
      if (value !== undefined) {
        result[targetField] = value;
      }
    }
  });

  return result;
}

/**
 * 应用数据映射到数据数组
 * @param sourceDataArray 源数据数组
 * @param mapping 映射配置：目标字段 -> 源字段路径
 * @returns 映射后的数据数组
 */
export function applyDataMappingToArray(sourceDataArray: any[], mapping: DataMapping): any[] {
  if (!Array.isArray(sourceDataArray) || !mapping || Object.keys(mapping).length === 0) {
    return []; // 没有映射配置时返回空数组
  }

  return sourceDataArray.map(item => applyDataMapping(item, mapping));
}

/**
 * 递归应用数据映射到树形数据
 * @param sourceDataArray 源数据数组
 * @param mapping 映射配置：目标字段 -> 源字段路径
 * @param childrenField 子节点字段名，默认为 'children'
 * @returns 映射后的树形数据数组
 */
export function applyDataMappingToTree(
  sourceDataArray: any[],
  mapping: DataMapping,
  childrenField: string = 'children'
): any[] {
  if (!Array.isArray(sourceDataArray) || !mapping || Object.keys(mapping).length === 0) {
    return []; // 没有映射配置时返回空数组
  }

  const mapNode = (node: any): any => {
    // 应用映射到当前节点
    const mappedNode = applyDataMapping(node, mapping);

    // 递归处理子节点
    if (node[childrenField] && Array.isArray(node[childrenField])) {
      mappedNode[childrenField] = node[childrenField].map(mapNode);
    }

    return mappedNode;
  };

  return sourceDataArray.map(mapNode);
}

/**
 * 验证数据映射配置
 * @param mapping 映射配置
 * @param requiredFields 必需字段列表
 * @returns 验证结果
 */
export function validateDataMapping(mapping: DataMapping, requiredFields: string[] = []): {
  isValid: boolean;
  missingFields: string[];
  errors: string[];
} {
  const missingFields: string[] = [];
  const errors: string[] = [];

  // 检查必需字段
  requiredFields.forEach(field => {
    if (!mapping[field]) {
      missingFields.push(field);
    }
  });

  // 检查映射路径格式
  Object.entries(mapping).forEach(([targetField, sourcePath]) => {
    if (sourcePath && typeof sourcePath !== 'string') {
      errors.push(`字段 "${targetField}" 的映射路径必须是字符串`);
    }
  });

  return {
    isValid: missingFields.length === 0 && errors.length === 0,
    missingFields,
    errors
  };
}

/**
 * 从示例数据中自动推断映射关系
 * @param sampleData 示例数据数组
 * @param targetFields 目标字段列表
 * @returns 推断的映射配置
 */
export function inferDataMapping(sampleData: any[], targetFields: string[]): DataMapping {
  if (!Array.isArray(sampleData) || sampleData.length === 0) {
    return {};
  }

  const mapping: DataMapping = {};
  const firstItem = sampleData[0];

  targetFields.forEach(targetField => {
    // 直接匹配字段名
    if (firstItem.hasOwnProperty(targetField)) {
      mapping[targetField] = targetField;
      return;
    }

    // 尝试模糊匹配
    const lowerTargetField = targetField.toLowerCase();
    const matchingKey = Object.keys(firstItem).find(key => 
      key.toLowerCase() === lowerTargetField ||
      key.toLowerCase().includes(lowerTargetField) ||
      lowerTargetField.includes(key.toLowerCase())
    );

    if (matchingKey) {
      mapping[targetField] = matchingKey;
    }
  });

  return mapping;
}
