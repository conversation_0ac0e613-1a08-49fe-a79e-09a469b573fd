{"version": "1.0.0", "meta": {"title": "API管理系统", "description": "API监控和管理平台"}, "theme": "dataSecurityTheme", "layout": {"type": "admin-layout", "components": {"topNavigation": "mainNav", "sidebar": "apiTreeNav", "content": "apiTableView", "statusBar": "systemStatus"}}, "components": {"mainNav": {"type": "TopNavigation", "props": {"logo": {"text": "全知科技", "icon": "/logo.png"}, "menus": [{"label": "概览", "key": "overview", "active": false}, {"label": "APIs", "key": "apis", "active": true}, {"label": "数据", "key": "data", "active": false}, {"label": "销售", "key": "sales", "active": false}, {"label": "风险", "key": "risk", "active": false}, {"label": "审计", "key": "audit", "active": false}, {"label": "报警", "key": "alert", "active": false}, {"label": "多方", "key": "multiparty", "active": false}, {"label": "登录", "key": "login", "active": false}, {"label": "配置", "key": "config", "active": false}], "userInfo": {"avatar": "/avatar.png", "name": "block", "showNotification": true, "notificationCount": 1}}}, "apiTreeNav": {"type": "SidebarTreeView", "props": {"title": "数据范围", "searchable": true, "searchPlaceholder": "搜索API", "tree": [{"key": "all-apis", "title": "全部API", "count": 212, "icon": "api", "children": [{"key": "penetration-apis", "title": "渗透测试查询API", "count": null, "children": [{"key": "data-api", "title": "数据API", "count": 26}, {"key": "url-api", "title": "URL重定向API", "count": 5}, {"key": "common-api", "title": "常见信息数据维护工作", "count": 3}, {"key": "short-api", "title": "短信验证码发送API", "count": 2}, {"key": "register-api", "title": "注册API", "count": 0}]}, {"key": "software-api", "title": "五软件管理API", "count": null}, {"key": "api-group", "title": "API组织", "count": null}, {"key": "api-config", "title": "API配置", "count": null}]}], "defaultSelected": "all-apis"}, "dataBinding": {"source": "getApiTree", "autoLoad": true}}, "apiTableView": {"type": "TableViewWithSearch", "props": {"title": "全部API", "subtitle": "共有人员：共计7名", "searchConfig": {"fields": [{"name": "appName", "label": "应用", "type": "select", "placeholder": "请选择", "options": "getAppOptions"}, {"name": "apiLevel", "label": "等级", "type": "select", "placeholder": "请选择", "options": [{"label": "高", "value": "high"}, {"label": "中", "value": "medium"}, {"label": "低", "value": "low"}]}, {"name": "apiName", "label": "API名称", "type": "input", "placeholder": "请输入API名称"}, {"name": "riskLevel", "label": "风险等级", "type": "select", "placeholder": "请选择", "options": [{"label": "高风险", "value": "high"}, {"label": "中风险", "value": "medium"}, {"label": "低风险", "value": "low"}]}], "layout": "inline", "showReset": true, "showSearch": true}, "toolbar": {"left": [{"type": "tabs", "items": [{"label": "高风险 (58)", "key": "high", "active": true}, {"label": "中风险 (3)", "key": "medium"}, {"label": "低风险 (19)", "key": "low"}, {"label": "非风险 (132)", "key": "none"}]}], "right": [{"type": "button", "label": "导出风险报告", "icon": "export"}, {"type": "button", "label": "风险配置", "icon": "setting", "style": "primary"}]}, "table": {"columns": [{"title": "等级", "dataIndex": "level", "type": "tag", "width": 80, "mapping": {"high": {"text": "高风险", "color": "red"}, "medium": {"text": "中风险", "color": "orange"}, "low": {"text": "低风险", "color": "blue"}}}, {"title": "API路径等级分组", "dataIndex": "<PERSON><PERSON><PERSON><PERSON>", "type": "text", "width": 300}, {"title": "API风险等级", "dataIndex": "riskLevel", "type": "text", "width": 120}, {"title": "实计访问次数", "dataIndex": "accessCount", "type": "number", "width": 120, "sortable": true}, {"title": "使用频率", "dataIndex": "frequency", "type": "text", "width": 100}, {"title": "首次发现时间", "dataIndex": "firstFoundTime", "type": "datetime", "width": 180, "sortable": true}, {"title": "操作", "type": "actions", "width": 100, "actions": [{"label": "详情", "type": "drawer", "target": "apiDetailDrawer", "icon": "eye"}, {"label": "更多", "type": "dropdown", "items": [{"label": "编辑", "action": "edit"}, {"label": "删除", "action": "delete", "confirm": "确定删除吗？"}]}]}], "pagination": {"pageSize": 25, "showSizeChanger": true, "showQuickJumper": true, "showTotal": true}, "selection": {"type": "checkbox", "showSelectAll": true}}, "summary": {"show": true, "template": "共 58 条，第 1 / 3 页"}}, "dataBinding": {"source": "getApiList", "autoLoad": true, "params": {"category": "high-risk"}}}, "systemStatus": {"type": "StatusBar", "props": {"left": {"copyright": "Copyright © 2017-2025 All rights reserved."}, "right": {"version": "版本：3.3.0.20250731020635"}}}}, "apis": {"getApiTree": {"url": "/api/tree/apis", "method": "GET", "responseMapping": "data"}, "getAppOptions": {"url": "/api/options/apps", "method": "GET", "responseMapping": "data"}, "getApiList": {"url": "/api/apis", "method": "GET", "params": ["category", "appName", "apiLevel", "apiName", "riskLevel", "page", "pageSize"], "responseMapping": "data"}}}