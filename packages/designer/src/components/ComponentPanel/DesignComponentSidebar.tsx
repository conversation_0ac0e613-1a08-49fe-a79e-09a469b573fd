import React, { useState } from 'react';
import { useDesigner } from '../../context/DesignerContext';
import { createDefaultComponentSchema } from '@lowcode/shared';

export interface DesignComponentSidebarProps {
  className?: string;
  style?: React.CSSProperties;
}

// 组件数据定义
interface ComponentItem {
  key: string;
  label: string;
  icon: string;
}

interface TabData {
  key: string;
  label: string;
  icon: string;
  items: ComponentItem[];
}

const tabsData: TabData[] = [
  {
    key: 'layout',
    label: '布局',
    icon: 'view_quilt',
    items: [
      { key: 'TopDownLayout', label: '上下布局', icon: 'view_quilt' }
    ]
  },
  {
    key: 'business',
    label: '业务',
    icon: 'business_center',
    items: [
      { key: 'TopNavigation', label: '顶部导航', icon: 'navigation' },
      { key: 'SidebarTreeView', label: '视图列表', icon: 'view_list' },
      { key: 'TableViewWithSearch', label: '云效表格', icon: 'table_view' },
      { key: 'StatusBar', label: '状态栏', icon: 'info' }
    ]
  }
];

export const DesignComponentSidebar: React.FC<DesignComponentSidebarProps> = ({
  className,
  style
}) => {
  const { addComponent, canvasState } = useDesigner();
  const [activeTab, setActiveTab] = useState('business');
  const isCollapsed = canvasState.componentPanelCollapsed;

  // 处理组件点击
  const handleComponentClick = (componentKey: string) => {
    const schema = createDefaultComponentSchema(componentKey);
    addComponent(schema);
  };

  // 渲染组件项 - 像素级还原设计稿样式
  const renderComponentItem = (item: ComponentItem) => (
    <div
      key={item.key}
      className="lc-card"
      onClick={() => handleComponentClick(item.key)}
      style={{
        padding: '10px 8px',
        border: '1px solid #e5e7eb',
        borderRadius: '8px',
        textAlign: 'center',
        cursor: 'move',
        transition: 'all 0.2s ease',
        backgroundColor: '#ffffff',
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        minHeight: '60px',
        justifyContent: 'center'
      }}
      onMouseEnter={(e) => {
        e.currentTarget.style.backgroundColor = '#f9fafb';
        e.currentTarget.style.borderColor = '#3b82f6';
        e.currentTarget.style.transform = 'translateY(-1px)';
        e.currentTarget.style.boxShadow = '0 2px 8px rgba(0, 0, 0, 0.1)';
      }}
      onMouseLeave={(e) => {
        e.currentTarget.style.backgroundColor = '#ffffff';
        e.currentTarget.style.borderColor = '#e5e7eb';
        e.currentTarget.style.transform = 'translateY(0)';
        e.currentTarget.style.boxShadow = 'none';
      }}
    >
      <span
        style={{
          fontFamily: 'Material Icons',
          fontSize: '22px',
          color: '#6b7280',
          display: 'block',
          marginBottom: '3px',
          lineHeight: '1'
        }}
      >
        {item.icon}
      </span>
      <span style={{
        fontSize: '12px',
        color: '#1f2937',
        fontWeight: '500',
        lineHeight: '1.2'
      }}>
        {item.label}
      </span>
    </div>
  );

  // 渲染基础组件分组
  const renderBasicsContent = () => {
    const basicsTab = tabsData.find(tab => tab.key === 'basics');
    if (!basicsTab) return null;

    return (
      <div className="lc-grid" style={{
        display: 'grid',
        gridTemplateColumns: '1fr',
        gap: '8px'
      }}>
        {basicsTab.items.map(renderComponentItem)}
      </div>
    );
  };

  // 渲染标签页内容
  const renderTabContent = () => {
    const currentTab = tabsData.find(tab => tab.key === activeTab);
    if (!currentTab) return null;

    if (activeTab === 'basics') {
      return renderBasicsContent();
    }

    return (
      <div className="lc-grid" style={{
        display: 'grid',
        gridTemplateColumns: '1fr',
        gap: '8px'
      }}>
        {currentTab.items.map(renderComponentItem)}
      </div>
    );
  };

  return (
    <aside
      className={`lc-component-library ${className || ''}`}
      style={{
        width: isCollapsed ? '0px' : '220px',
        backgroundColor: '#fafafa',
        borderRight: isCollapsed ? 'none' : '1px solid #f0f0f0',
        display: 'flex',
        flexDirection: 'row',
        flexShrink: 0,
        transition: 'all 0.3s ease',
        position: 'relative',
        overflow: 'auto',
        ...style
      }}
    >

      {/* 侧边栏内容 */}
      <div 
        style={{
          opacity: isCollapsed ? 0 : 1,
          transition: 'opacity 0.3s',
          display: 'flex',
          flex: 1,
          overflow: 'hidden'
        }}
      >
        {/* 左侧标签栏 */}
        <div className="lc-sidebar-nav" style={{
          width: '80px',
          backgroundColor: '#ffffff',
          borderRight: '1px solid #e5e7eb',
          flexShrink: 0,
          display: 'flex',
          flexDirection: 'column'
        }}>
          <nav className="lc-nav-inner" style={{
            display: 'flex',
            flexDirection: 'column',
            gap: '4px',
            padding: '8px'
          }}>
            {tabsData.map(tab => (
              <button
                key={tab.key}
                onClick={() => setActiveTab(tab.key)}
                className={`lc-tab ${activeTab === tab.key ? 'lc-tab-active' : 'lc-tab-inactive'}`}
                style={{
                  display: 'flex',
                  flexDirection: 'column',
                  alignItems: 'center',
                  padding: '8px 6px',
                  borderLeft: activeTab === tab.key ? '3px solid #3b82f6' : '3px solid transparent',
                  borderRadius: '6px',
                  border: 'none',
                  backgroundColor: activeTab === tab.key ? '#eff6ff' : 'transparent',
                  color: activeTab === tab.key ? '#2563eb' : '#6b7280',
                  cursor: 'pointer',
                  transition: 'color 0.2s, background-color 0.2s',
                  position: 'relative'
                }}
                onMouseEnter={(e) => {
                  if (activeTab !== tab.key) {
                    e.currentTarget.style.backgroundColor = '#f1f5f9';
                    e.currentTarget.style.color = '#374151';
                    e.currentTarget.style.transform = 'scale(1.05)';
                  }
                }}
                onMouseLeave={(e) => {
                  if (activeTab !== tab.key) {
                    e.currentTarget.style.backgroundColor = 'transparent';
                    e.currentTarget.style.color = '#6b7280';
                    e.currentTarget.style.transform = 'scale(1)';
                  }
                }}
              >
                <span style={{
                  fontFamily: 'Material Icons',
                  fontSize: '18px',
                  marginBottom: '2px',
                  lineHeight: '1'
                }}>
                  {tab.icon}
                </span>
                <span style={{
                  fontSize: '10px',
                  fontWeight: '600',
                  letterSpacing: '0.2px'
                }}>
                  {tab.label}
                </span>
              </button>
            ))}
          </nav>
        </div>

        {/* 右侧内容区 */}
        <div className="lc-panel" style={{
          flex: 1,
          overflowY: 'auto',
          transition: 'opacity 0.3s ease',
          backgroundColor: '#fafafa',
          padding: '12px 8px'
        }}>
          {renderTabContent()}
        </div>
      </div>
    </aside>
  );
};
