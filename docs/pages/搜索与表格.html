<!DOCTYPE html>
<html lang="en"><head>
<meta charset="utf-8"/>
<meta content="width=device-width, initial-scale=1.0" name="viewport"/>
<title>API Management</title>
<script src="https://cdn.tailwindcss.com?plugins=forms,container-queries"></script>
<link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet"/>
<style type="text/tailwindcss">
    body {
      background-color: #ffffff;
    }
  </style>
</head>
<body class="p-8">
<div class="bg-white p-6 rounded-lg shadow-sm" style="width: calc(100% + 100px);">
<div class="bg-[#f3f4f5] p-6 rounded-lg">
<div class="flex items-center justify-between mb-6">
<h1 class="text-xl font-semibold">全部API</h1>
<div class="flex items-center space-x-4">
<div class="relative">
<input class="w-64 pl-4 pr-10 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="请输入URL进行筛选" type="text"/>
<span class="material-icons absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400">search</span>
</div>
<div class="flex items-center space-x-2 text-gray-600">
<span class="material-icons cursor-pointer">filter_alt</span>
<span class="material-icons cursor-pointer">refresh</span>
<span class="material-icons cursor-pointer">view_column</span>
</div>
<button class="bg-blue-500 text-white px-4 py-2 rounded-md flex items-center">
                        批量操作
                        <span class="material-icons text-sm ml-1">arrow_drop_down</span>
</button>
<button class="bg-gray-100 text-gray-700 px-4 py-2 rounded-md">保存视图</button>
</div>
</div>
<div class="grid grid-cols-1 md:grid-cols-2 gap-x-12 gap-y-4 mb-6">
<div class="flex items-center">
<label class="w-28 text-gray-600 flex-shrink-0">应用</label>
<input class="w-full border rounded-md px-3 py-2" placeholder="请输入" type="text"/>
</div>
<div class="flex items-center">
<label class="w-28 text-gray-600 flex-shrink-0">API敏感等级</label>
<select class="w-full border rounded-md px-3 py-2 bg-white">
<option>请选择</option>
</select>
</div>
<div class="flex items-center">
<label class="w-28 text-gray-600 flex-shrink-0">应用名称</label>
<input class="w-full border rounded-md px-3 py-2" placeholder="请输入" type="text"/>
</div>
<div class="flex items-center">
<label class="w-28 text-gray-600 flex-shrink-0">API标签</label>
<div class="flex items-center w-full">
<select class="w-1/3 border-r-0 rounded-l-md px-3 py-2 bg-white">
<option>或</option>
</select>
<select class="w-2/3 border rounded-r-md px-3 py-2 bg-white">
<option>请选择</option>
</select>
</div>
</div>
<div class="flex items-center">
<label class="w-28 text-gray-600 flex-shrink-0">访问域</label>
<div class="flex items-center w-full">
<select class="w-1/3 border-r-0 rounded-l-md px-3 py-2 bg-white">
<option>或</option>
</select>
<select class="w-2/3 border rounded-r-md px-3 py-2 bg-white">
<option>请选择</option>
</select>
</div>
</div>
<div class="flex items-center">
<label class="w-28 text-gray-600 flex-shrink-0">部署域</label>
<div class="flex items-center w-full">
<select class="w-1/3 border-r-0 rounded-l-md px-3 py-2 bg-white">
<option>或</option>
</select>
<select class="w-2/3 border rounded-r-md px-3 py-2 bg-white">
<option>请选择</option>
</select>
</div>
</div>
<div class="flex items-center">
<label class="w-28 text-gray-600 flex-shrink-0">终端类型</label>
<div class="flex items-center w-full">
<select class="w-1/3 border-r-0 rounded-l-md px-3 py-2 bg-white">
<option>或</option>
</select>
<select class="w-2/3 border rounded-r-md px-3 py-2 bg-white">
<option>请选择</option>
</select>
</div>
</div>
<div class="flex items-center">
<label class="w-28 text-gray-600 flex-shrink-0">请求类型</label>
<select class="w-full border rounded-md px-3 py-2 bg-white">
<option>请选择</option>
</select>
</div>
<div class="flex items-center">
<label class="w-28 text-gray-600 flex-shrink-0">响应类型</label>
<select class="w-full border rounded-md px-3 py-2 bg-white">
<option>请选择</option>
</select>
</div>
<div class="flex items-center">
<label class="w-28 text-gray-600 flex-shrink-0">响应数据标签</label>
<div class="flex items-center w-full">
<select class="w-1/3 border-r-0 rounded-l-md px-3 py-2 bg-white">
<option>或</option>
</select>
<select class="flex-grow border rounded-r-md px-3 py-2 bg-white">
<option>请选择</option>
</select>
</div>
<div class="flex items-center ml-4 flex-shrink-0">
<input class="mr-2" type="checkbox"/>
<span class="text-gray-600 whitespace-nowrap">按单例/桶</span>
<span class="material-icons text-gray-400 ml-1 text-base">info</span>
</div>
</div>
<div class="flex items-center col-span-2">
<label class="w-28 text-gray-600 flex-shrink-0">首次发现时间</label>
<div class="flex items-center space-x-2">
<select class="border rounded-md px-3 py-2 bg-white">
<option>自定义时间</option>
</select>
<input class="w-40 border rounded-md px-3 py-2" placeholder="开始日期" type="text"/>
<span class="text-gray-400">-</span>
<input class="w-40 border rounded-md px-3 py-2" placeholder="结束日期" type="text"/>
</div>
</div>
<div class="flex items-center justify-end col-span-2 mt-4">
<button class="text-blue-500 mr-4">更多搜索条件</button>
<button class="bg-gray-200 text-gray-700 px-4 py-2 rounded-md mr-2">取消</button>
<button class="bg-blue-500 text-white px-4 py-2 rounded-md">确定</button>
</div>
</div>
</div>
<div class="mt-6">
<div class="flex items-center space-x-6 mb-4">
<span class="text-gray-600">按API风险等级分组:</span>
<button class="text-blue-500 font-semibold pb-2 border-b-2 border-blue-500">高风险 (34)</button>
<button class="text-gray-600 pb-2">中风险 (38)</button>
<button class="text-gray-600 pb-2">低风险 (8)</button>
<button class="text-gray-600 pb-2">无风险 (124)</button>
<button class="text-gray-600 pb-2">其他 (8)</button>
<button class="text-blue-500 ml-4">取消分组</button>
</div>
<table class="w-full text-left table-auto">
<thead class="bg-[#eaedf4]">
<tr>
<th class="p-3 w-12"><input type="checkbox"/></th>
<th class="p-3 w-4/12 font-normal text-gray-600">路径</th>
<th class="p-3 w-2/12 font-normal text-gray-600 whitespace-nowrap">API敏感等级</th>
<th class="p-3 w-2/12 font-normal text-gray-600 whitespace-nowrap">API风险等级</th>
<th class="p-3 w-2/12 font-normal text-gray-600 flex items-center whitespace-nowrap">累计访问次数 <span class="material-icons text-gray-400 ml-1 text-sm">info</span></th>
<th class="p-3 w-2/12 font-normal text-gray-600">流量来源</th>
<th class="p-3 w-3/12 font-normal text-gray-600">首次发现时间</th>
<th class="p-3 w-1/12 font-normal text-gray-600">操作</th>
</tr>
</thead>
<tbody class="bg-white">
<tr class="border-b">
<td class="p-3"><input type="checkbox"/></td>
<td class="p-3 truncate">
<span class="bg-red-100 text-red-700 text-xs font-semibold mr-2 px-2 py-1 rounded">高敏感</span> /login
                        </td>
<td class="p-3">高敏感</td>
<td class="p-3">高风险</td>
<td class="p-3 text-blue-500">3.6千</td>
<td class="p-3">192.168.0.1</td>
<td class="p-3">2025-08-14 19:18:21</td>
<td class="p-3 flex items-center space-x-2 text-gray-500">
<span class="material-icons cursor-pointer">content_copy</span>
<span class="material-icons cursor-pointer">more_horiz</span>
</td>
</tr>
<tr class="border-b">
<td class="p-3"><input type="checkbox"/></td>
<td class="p-3 truncate">
<span class="bg-red-100 text-red-700 text-xs font-semibold mr-2 px-2 py-1 rounded">高敏感</span> /abnormal
                        </td>
<td class="p-3">高敏感</td>
<td class="p-3">高风险</td>
<td class="p-3 text-blue-500">2.5千</td>
<td class="p-3">192.168.0.1</td>
<td class="p-3">2025-08-14 19:19:12</td>
<td class="p-3 flex items-center space-x-2 text-gray-500">
<span class="material-icons cursor-pointer">content_copy</span>
<span class="material-icons cursor-pointer">more_horiz</span>
</td>
</tr>
</tbody>
</table>
<div class="flex items-center justify-between mt-4">
<span class="text-gray-600">共 34 条 第 1 / 2 页</span>
<div class="flex items-center">
<button class="p-2 border rounded-md mr-2">
<span class="material-icons text-sm">chevron_left</span>
</button>
<button class="p-2 border rounded-md">
<span class="material-icons text-sm">chevron_right</span>
</button>
<select class="ml-4 border rounded-md p-2 bg-white">
<option>25条/页</option>
</select>
</div>
</div>
</div>
</div>
</body></html>